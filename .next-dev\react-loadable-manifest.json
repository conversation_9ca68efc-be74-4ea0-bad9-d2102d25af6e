{"app\\cas\\[id]\\page.tsx -> @/app/components/Button": {"id": "app\\cas\\[id]\\page.tsx -> @/app/components/Button", "files": []}, "app\\cas\\[id]\\page.tsx -> @/app/components/CasMap": {"id": "app\\cas\\[id]\\page.tsx -> @/app/components/CasMap", "files": ["static/chunks/_app-pages-browser_app_components_CasMap_tsx.js"]}, "app\\cas\\[id]\\page.tsx -> @/app/components/FormError": {"id": "app\\cas\\[id]\\page.tsx -> @/app/components/FormError", "files": ["static/chunks/_app-pages-browser_app_components_FormError_tsx.js"]}, "app\\cas\\[id]\\page.tsx -> @/app/components/Input": {"id": "app\\cas\\[id]\\page.tsx -> @/app/components/Input", "files": ["static/chunks/_app-pages-browser_app_components_Input_tsx.js"]}, "app\\cas\\[id]\\page.tsx -> @/app/components/LoadingSpinner": {"id": "app\\cas\\[id]\\page.tsx -> @/app/components/LoadingSpinner", "files": []}, "app\\cas\\[id]\\page.tsx -> @/app/components/Modal": {"id": "app\\cas\\[id]\\page.tsx -> @/app/components/Modal", "files": ["static/chunks/_app-pages-browser_app_components_Modal_tsx.js"]}, "app\\cas\\[id]\\page.tsx -> @/app/components/TextArea": {"id": "app\\cas\\[id]\\page.tsx -> @/app/components/TextArea", "files": ["static/chunks/_app-pages-browser_app_components_TextArea_tsx.js"]}, "app\\components\\CasMap.tsx -> leaflet": {"id": "app\\components\\CasMap.tsx -> leaflet", "files": ["static/chunks/_app-pages-browser_node_modules_leaflet_dist_leaflet-src_js.js"]}, "node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}}