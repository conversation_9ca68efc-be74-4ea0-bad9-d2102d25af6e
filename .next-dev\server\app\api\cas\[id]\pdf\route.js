/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cas/[id]/pdf/route";
exports.ids = ["app/api/cas/[id]/pdf/route"];
exports.modules = {

/***/ "(rsc)/./app/api/cas/[id]/pdf/route.ts":
/*!***************************************!*\
  !*** ./app/api/cas/[id]/pdf/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var pdfkit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! pdfkit */ \"pdfkit\");\n/* harmony import */ var pdfkit__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(pdfkit__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\n// Utility function to format dates\nconst formatDate = (date)=>{\n    if (!date) return \"Non spécifié\";\n    try {\n        const d = new Date(date);\n        if (isNaN(d.getTime())) return \"Non spécifié\";\n        return d.toLocaleDateString(\"fr-FR\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    } catch  {\n        return \"Non spécifié\";\n    }\n};\n// Utility function to get status label\n// Utility function to get status label\nconst getStatusLabel = (status)=>{\n    if (!status) return \"En attente\";\n    const statusMap = {\n        ACCEPTE: \"Accepté\",\n        AJOURNE: \"Ajourné\",\n        REJETE: \"Rejeté\",\n        ATTENTE: \"En attente\"\n    };\n    return statusMap[status] || status;\n};\n// Utility function to get person type label\n// Utility function to get person type label\nconst getPersonTypeLabel = (type)=>{\n    if (!type) return \"Non spécifié\";\n    const typeMap = {\n        PHYSIQUE: \"Personne Physique\",\n        MORALE: \"Personne Morale\"\n    };\n    return typeMap[type] || type;\n};\nasync function GET(req, { params }) {\n    try {\n        const { id: casId } = await params;\n        // Authentication check\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.forbidden)(\"Token non fourni.\");\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token);\n        if (!userPayload) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.forbidden)(\"Token invalide.\");\n        // Fetch case with all related data\n        const cas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.findUnique({\n            where: {\n                id: casId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        username: true,\n                        role: true\n                    }\n                },\n                problematique: {\n                    include: {\n                        encrage: true\n                    }\n                },\n                blocage: {\n                    include: {\n                        secteur: true\n                    },\n                    orderBy: {\n                        createdAt: \"desc\"\n                    }\n                },\n                communes: true\n            }\n        });\n        if (!cas) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.notFound)(\"Cas non trouvé.\");\n        }\n        // Create PDF document with error handling\n        // Create PDF document with minimal configuration to avoid font issues\n        const doc = new (pdfkit__WEBPACK_IMPORTED_MODULE_5___default())({\n            margin: 50,\n            size: \"A4\",\n            bufferPages: true\n        });\n        const chunks = [];\n        let pdfError = null;\n        doc.on(\"data\", (chunk)=>{\n            try {\n                chunks.push(chunk);\n            } catch (error) {\n                console.error(\"Error collecting PDF chunk:\", error);\n                pdfError = error;\n            }\n        });\n        doc.on(\"error\", (error)=>{\n            console.error(\"PDFKit error:\", error);\n            pdfError = error;\n        });\n        try {\n            // PDF Header - avoid font switching, use default font only\n            doc.fontSize(20).text(\"FICHE DE DOSSIER\", {\n                align: \"center\"\n            });\n            doc.moveDown(0.5);\n            doc.fontSize(14).text(`Dossier N° ${cas.id}`, {\n                align: \"center\"\n            });\n            doc.moveDown(1);\n            // Case Information Section\n            doc.fontSize(16).text(\"INFORMATIONS GÉNÉRALES\");\n            doc.moveDown(0.5);\n            const yStart = doc.y;\n            doc.fontSize(12);\n            // Left column - with safe property access\n            doc.text(`Nom du dossier: ${cas.nom || \"Non spécifié\"}`, 50, doc.y);\n            doc.text(`Type de personne: ${getPersonTypeLabel(cas.genre)}`, 50, doc.y + 20);\n            doc.text(`${cas.genre === \"PHYSIQUE\" ? \"NIN\" : \"NIF\"}: ${cas.genre === \"PHYSIQUE\" ? cas.nin || \"Non spécifié\" : cas.nif || \"Non spécifié\"}`, 50, doc.y + 20);\n            doc.text(`Superficie: ${cas.superficie ? `${cas.superficie} m²` : \"Non spécifiée\"}`, 50, doc.y + 20);\n            doc.text(`Date de dépôt: ${formatDate(cas.date_depot)}`, 50, doc.y + 20);\n            // Right column\n            doc.text(`DSA: Non spécifiée`, 300, yStart);\n            doc.text(`Communes: ${cas.communes && cas.communes.length > 0 ? cas.communes.map((c)=>c.nom).join(\", \") : \"Non spécifiées\"}`, 300, yStart + 20);\n            doc.text(`Statut: ${cas.regularisation ? \"Régularisé\" : \"Non régularisé\"}`, 300, yStart + 40);\n            doc.text(`Créé le: ${formatDate(cas.createdAt)}`, 300, yStart + 60);\n            doc.text(`Créé par: ${cas.user?.username || \"Inconnu\"}`, 300, yStart + 80);\n            doc.y += 40;\n            doc.moveDown(1);\n            // Problematic Section\n            if (cas.problematique) {\n                doc.fontSize(16).text(\"PROBLÉMATIQUE ET ENCRAGE\");\n                doc.moveDown(0.5);\n                doc.fontSize(12);\n                doc.text(`Problématique: ${cas.problematique.problematique || \"Non spécifiée\"}`);\n                if (cas.problematique.encrage) {\n                    doc.text(`Encrage: ${cas.problematique.encrage.nom || \"Non spécifié\"}`);\n                }\n                doc.moveDown(1);\n            }\n            // Observations Section\n            if (cas.observation) {\n                doc.fontSize(16).text(\"OBSERVATIONS\");\n                doc.moveDown(0.5);\n                doc.fontSize(12);\n                doc.text(cas.observation, {\n                    width: 500,\n                    align: \"justify\"\n                });\n                doc.moveDown(1);\n            }\n            // Blocages Section\n            if (cas.blocage && cas.blocage.length > 0) {\n                doc.fontSize(16).text(\"CONTRAINTES ET BLOCAGES\");\n                doc.moveDown(0.5);\n                cas.blocage.forEach((blocage, index)=>{\n                    // Check if we need a new page\n                    if (doc.y > 700) {\n                        doc.addPage();\n                    }\n                    // Type-safe access to blocage properties\n                    const blocageWithResolution = blocage;\n                    doc.fontSize(14).text(`Contrainte ${index + 1}:`);\n                    doc.fontSize(12);\n                    doc.text(`Description: ${blocage.description || \"Non spécifiée\"}`);\n                    doc.text(`Secteur: ${blocage.secteur?.secteur || blocage.secteur?.nom || \"Non spécifié\"}`);\n                    doc.text(`Date de création: ${formatDate(blocage.createdAt)}`);\n                    doc.text(`Date du blocage: ${formatDate(blocage.blocageDate)}`);\n                    const resolution = blocageWithResolution.resolution || \"ATTENTE\";\n                    doc.text(`Statut: ${getStatusLabel(resolution)}`);\n                    if (blocageWithResolution.resolutionDate) {\n                        doc.text(`Date de résolution: ${formatDate(blocageWithResolution.resolutionDate)}`);\n                    }\n                    if (blocageWithResolution.detailResolution) {\n                        doc.text(`Détail de résolution: ${blocageWithResolution.detailResolution}`);\n                    }\n                    doc.text(`Régularisé: ${blocage.regularise ? \"Oui\" : \"Non\"}`);\n                    doc.moveDown(0.5);\n                    // Add separator line\n                    if (index < cas.blocage.length - 1) {\n                        doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();\n                        doc.moveDown(0.5);\n                    }\n                });\n            } else {\n                doc.fontSize(16).text(\"CONTRAINTES ET BLOCAGES\");\n                doc.moveDown(0.5);\n                doc.fontSize(12).text(\"Aucune contrainte enregistrée pour ce dossier.\");\n                doc.moveDown(1);\n            }\n            // Footer\n            doc.fontSize(10).text(`Document généré le ${formatDate(new Date())} - Système de Gestion des Dossiers`, 50, 750, {\n                align: \"center\"\n            });\n        } catch (pdfGenerationError) {\n            console.error(\"Error during PDF content generation:\", pdfGenerationError);\n            throw new Error(`PDF generation failed: ${pdfGenerationError}`);\n        }\n        // Finalize PDF\n        doc.end();\n        // Wait for PDF generation to complete with timeout\n        await new Promise((resolve, reject)=>{\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"PDF generation timeout\"));\n            }, 30000); // 30 second timeout\n            doc.on(\"end\", ()=>{\n                clearTimeout(timeout);\n                if (pdfError) {\n                    reject(pdfError);\n                } else {\n                    resolve();\n                }\n            });\n            doc.on(\"error\", (error)=>{\n                clearTimeout(timeout);\n                reject(error);\n            });\n        });\n        if (chunks.length === 0) {\n            throw new Error(\"No PDF data generated\");\n        }\n        const pdfBuffer = Buffer.concat(chunks);\n        if (pdfBuffer.length === 0) {\n            throw new Error(\"Empty PDF buffer generated\");\n        }\n        // Generate filename\n        const timestamp = new Date().toISOString().split(\"T\")[0];\n        const safeName = cas.nom?.replace(/[^a-zA-Z0-9]/g, \"_\") || \"Dossier\";\n        const filename = `Fiche_Cas_${cas.id}_${safeName}_${timestamp}.pdf`;\n        // Return PDF response\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                \"Content-Type\": \"application/pdf\",\n                \"Content-Disposition\": `attachment; filename=\"${filename}\"`,\n                \"Content-Length\": pdfBuffer.length.toString(),\n                \"Cache-Control\": \"no-cache\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Erreur lors de la génération du PDF:\", error);\n        // More detailed error logging\n        if (error instanceof Error) {\n            console.error(\"Error name:\", error.name);\n            console.error(\"Error message:\", error.message);\n            console.error(\"Error stack:\", error.stack);\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/cas/[id]/pdf/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   forbidden: () => (/* binding */ forbidden),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   notFound: () => (/* binding */ notFound),\n/* harmony export */   unauthorized: () => (/* binding */ unauthorized),\n/* harmony export */   updateCasRegularisationStatus: () => (/* binding */ updateCasRegularisationStatus)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n // Importez Prisma pour typer les erreurs spécifiques\n\nasync function updateCasRegularisationStatus(casId) {\n    const relatedCasBlocages = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.blocage.findMany({\n        where: {\n            casId: casId\n        },\n        select: {\n            regularise: true\n        }\n    });\n    const allBlocagesRegularised = relatedCasBlocages.length > 0 && relatedCasBlocages.every((b)=>b.regularise);\n    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.cas.update({\n        where: {\n            id: casId\n        },\n        data: {\n            regularisation: allBlocagesRegularised\n        }\n    });\n}\nfunction handleError(error) {\n    console.error(error); // Bon pour le débogage côté serveur\n    if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_1__.Prisma.PrismaClientKnownRequestError) {\n        // Erreurs connues de Prisma (contraintes uniques, etc.)\n        // Vous pouvez ajouter des codes d'erreur spécifiques ici si nécessaire\n        // Par exemple, P2002 pour violation de contrainte unique\n        if (error.code === \"P2002\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Une ressource avec ces identifiants existe déjà.\",\n                details: error.meta\n            }, {\n                status: 409\n            }); // Conflict\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur de base de données.\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n    if (error instanceof Error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Une erreur interne est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Une erreur inconnue est survenue.\"\n    }, {\n        status: 500\n    });\n}\nfunction forbidden(message = \"Accès interdit.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 403\n    });\n}\nfunction notFound(message = \"Ressource non trouvée.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 404\n    });\n}\nfunction unauthorized() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Non autorisé\"\n    }, {\n        status: 401\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute&page=%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute&page=%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_id_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/cas/[id]/pdf/route.ts */ \"(rsc)/./app/api/cas/[id]/pdf/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cas/[id]/pdf/route\",\n        pathname: \"/api/cas/[id]/pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/cas/[id]/pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\cas\\\\[id]\\\\pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_id_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute&page=%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pdfkit":
/*!*************************!*\
  !*** external "pdfkit" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("pdfkit");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute&page=%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2F%5Bid%5D%2Fpdf%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();