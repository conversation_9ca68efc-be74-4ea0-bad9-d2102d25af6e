import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { handleError, forbidden, notFound } from "@/lib/api-utils";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth";
import PDFDocument from "pdfkit";
import { Prisma } from "@prisma/client";

// Define proper types for the Prisma query result
// Define proper types for the Prisma query result
type CasWithRelations = Prisma.CasGetPayload<{
    include: {
        user: {
            select: { id: true; username: true; role: true };
        };
        problematique: {
            include: {
                encrage: true;
            };
        };
        blocage: {
            include: {
                secteur: true;
            };
        };
        communes: true;
    };
}>;

// Type for blocage with extended properties that might exist
type BlocageWithResolution = NonNullable<CasWithRelations["blocage"][0]> & {
    resolution?: string;
    resolutionDate?: Date | string | null;
    detailResolution?: string | null;
};

// Utility function to format dates
const formatDate = (date: Date | string | null | undefined): string => {
    if (!date) return "Non spécifié";
    try {
        const d = new Date(date);
        if (isNaN(d.getTime())) return "Non spécifié";
        return d.toLocaleDateString("fr-FR", {
            year: "numeric",
            month: "long",
            day: "numeric",
        });
    } catch {
        return "Non spécifié";
    }
};

// Utility function to get status label
// Utility function to get status label
const getStatusLabel = (status: string | undefined): string => {
    if (!status) return "En attente";
    const statusMap: Record<string, string> = {
        ACCEPTE: "Accepté",
        AJOURNE: "Ajourné",
        REJETE: "Rejeté",
        ATTENTE: "En attente",
    };
    return statusMap[status] || status;
};

// Utility function to get person type label
// Utility function to get person type label
const getPersonTypeLabel = (type: string | null | undefined): string => {
    if (!type) return "Non spécifié";
    const typeMap: Record<string, string> = {
        PHYSIQUE: "Personne Physique",
        MORALE: "Personne Morale",
    };
    return typeMap[type] || type;
};

 export async function GET(
     req: NextRequest,
     { params }: { params: Promise<{ id: string }> }
 ) {
     try {
         const { id: casId } = await params;

         // Authentication check
         const cookieStore = await cookies();
         const token = cookieStore.get("token")?.value;
         if (!token) return forbidden("Token non fourni.");

         const userPayload = await verifyToken(token);
         if (!userPayload) return forbidden("Token invalide.");

         // Fetch case with all related data
         const cas = await prisma.cas.findUnique({
             where: { id: casId },
             include: {
                 user: {
                     select: { id: true, username: true, role: true },
                 },
                 problematique: {
                     include: {
                         encrage: true,
                     },
                 },
                 blocage: {
                     include: {
                         secteur: true,
                     },
                     orderBy: {
                         createdAt: "desc",
                     },
                 },
                 communes: true,
             },
         });

         if (!cas) {
             return notFound("Cas non trouvé.");
         }

         // Create PDF document with error handling

         // Create PDF document with minimal configuration to avoid font issues
         const doc = new PDFDocument({
             margin: 50,
             size: "A4",
             bufferPages: true,
         });

         const chunks: Buffer[] = [];
         let pdfError: Error | null = null;

         doc.on("data", (chunk) => {
             try {
                 chunks.push(chunk);
             } catch (error) {
                 console.error("Error collecting PDF chunk:", error);
                 pdfError = error as Error;
             }
         });

         doc.on("error", (error) => {
             console.error("PDFKit error:", error);
             pdfError = error;
         });

         try {
             // PDF Header - avoid font switching, use default font only
             doc.fontSize(20).text("FICHE DE DOSSIER", { align: "center" });
             doc.moveDown(0.5);
             doc.fontSize(14).text(`Dossier N° ${cas.id}`, { align: "center" });
             doc.moveDown(1);

             // Case Information Section
             doc.fontSize(16).text("INFORMATIONS GÉNÉRALES");
             doc.moveDown(0.5);

             const yStart = doc.y;
             doc.fontSize(12);

             // Left column - with safe property access
             doc.text(
                 `Nom du dossier: ${cas.nom || "Non spécifié"}`,
                 50,
                 doc.y
             );
             doc.text(
                 `Type de personne: ${getPersonTypeLabel(cas.genre)}`,
                 50,
                 doc.y + 20
             );
             doc.text(
                 `${cas.genre === "PHYSIQUE" ? "NIN" : "NIF"}: ${
                     cas.genre === "PHYSIQUE"
                         ? cas.nin || "Non spécifié"
                         : cas.nif || "Non spécifié"
                 }`,
                 50,
                 doc.y + 20
             );
             doc.text(
                 `Superficie: ${
                     cas.superficie ? `${cas.superficie} m²` : "Non spécifiée"
                 }`,
                 50,
                 doc.y + 20
             );
             doc.text(
                 `Date de dépôt: ${formatDate(cas.date_depot)}`,
                 50,
                 doc.y + 20
             );

             // Right column
             doc.text(`DSA: Non spécifiée`, 300, yStart);
             doc.text(
                 `Communes: ${
                     cas.communes && cas.communes.length > 0
                         ? cas.communes.map((c) => c.nom).join(", ")
                         : "Non spécifiées"
                 }`,
                 300,
                 yStart + 20
             );
             doc.text(
                 `Statut: ${
                     cas.regularisation ? "Régularisé" : "Non régularisé"
                 }`,
                 300,
                 yStart + 40
             );
             doc.text(
                 `Créé le: ${formatDate(cas.createdAt)}`,
                 300,
                 yStart + 60
             );
             doc.text(
                 `Créé par: ${cas.user?.username || "Inconnu"}`,
                 300,
                 yStart + 80
             );

             doc.y += 40;
             doc.moveDown(1);

             // Problematic Section
             if (cas.problematique) {
                 doc.fontSize(16).text("PROBLÉMATIQUE ET ENCRAGE");
                 doc.moveDown(0.5);
                 doc.fontSize(12);

                 doc.text(
                     `Problématique: ${
                         cas.problematique.problematique || "Non spécifiée"
                     }`
                 );
                 if (cas.problematique.encrage) {
                     doc.text(
                         `Encrage: ${
                             cas.problematique.encrage.nom || "Non spécifié"
                         }`
                     );
                 }
                 doc.moveDown(1);
             }

             // Observations Section
             if (cas.observation) {
                 doc.fontSize(16).text("OBSERVATIONS");
                 doc.moveDown(0.5);
                 doc.fontSize(12);
                 doc.text(cas.observation, { width: 500, align: "justify" });
                 doc.moveDown(1);
             }

             // Blocages Section
             if (cas.blocage && cas.blocage.length > 0) {
                 doc.fontSize(16).text("CONTRAINTES ET BLOCAGES");
                 doc.moveDown(0.5);

                 cas.blocage.forEach((blocage, index) => {
                     // Check if we need a new page
                     if (doc.y > 700) {
                         doc.addPage();
                     }

                     // Type-safe access to blocage properties
                     const blocageWithResolution =
                         blocage as BlocageWithResolution;

                     doc.fontSize(14).text(`Contrainte ${index + 1}:`);
                     doc.fontSize(12);
                     doc.text(
                         `Description: ${
                             blocage.description || "Non spécifiée"
                         }`
                     );
                     doc.text(
                         `Secteur: ${
                             blocage.secteur?.secteur ||
                             blocage.secteur?.nom ||
                             "Non spécifié"
                         }`
                     );
                     doc.text(
                         `Date de création: ${formatDate(blocage.createdAt)}`
                     );
                     doc.text(
                         `Date du blocage: ${formatDate(blocage.blocageDate)}`
                     );

                     const resolution =
                         blocageWithResolution.resolution || "ATTENTE";
                     doc.text(`Statut: ${getStatusLabel(resolution)}`);

                     if (blocageWithResolution.resolutionDate) {
                         doc.text(
                             `Date de résolution: ${formatDate(
                                 blocageWithResolution.resolutionDate
                             )}`
                         );
                     }

                     if (blocageWithResolution.detailResolution) {
                         doc.text(
                             `Détail de résolution: ${blocageWithResolution.detailResolution}`
                         );
                     }

                     doc.text(
                         `Régularisé: ${blocage.regularise ? "Oui" : "Non"}`
                     );
                     doc.moveDown(0.5);

                     // Add separator line
                     if (index < cas.blocage.length - 1) {
                         doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();
                         doc.moveDown(0.5);
                     }
                 });
             } else {
                 doc.fontSize(16).text("CONTRAINTES ET BLOCAGES");
                 doc.moveDown(0.5);
                 doc.fontSize(12).text(
                     "Aucune contrainte enregistrée pour ce dossier."
                 );
                 doc.moveDown(1);
             }

             // Footer
             doc.fontSize(10).text(
                 `Document généré le ${formatDate(
                     new Date()
                 )} - Système de Gestion des Dossiers`,
                 50,
                 750,
                 { align: "center" }
             );
         } catch (pdfGenerationError) {
             console.error(
                 "Error during PDF content generation:",
                 pdfGenerationError
             );
             throw new Error(`PDF generation failed: ${pdfGenerationError}`);
         }

         // Finalize PDF
         doc.end();

         // Wait for PDF generation to complete with timeout
         await new Promise<void>((resolve, reject) => {
             const timeout = setTimeout(() => {
                 reject(new Error("PDF generation timeout"));
             }, 30000); // 30 second timeout

             doc.on("end", () => {
                 clearTimeout(timeout);
                 if (pdfError) {
                     reject(pdfError);
                 } else {
                     resolve();
                 }
             });

             doc.on("error", (error) => {
                 clearTimeout(timeout);
                 reject(error);
             });
         });

         if (chunks.length === 0) {
             throw new Error("No PDF data generated");
         }

         const pdfBuffer = Buffer.concat(chunks);

         if (pdfBuffer.length === 0) {
             throw new Error("Empty PDF buffer generated");
         }

         // Generate filename
         const timestamp = new Date().toISOString().split("T")[0];
         const safeName = cas.nom?.replace(/[^a-zA-Z0-9]/g, "_") || "Dossier";
         const filename = `Fiche_Cas_${cas.id}_${safeName}_${timestamp}.pdf`;

         // Return PDF response
         return new NextResponse(pdfBuffer, {
             status: 200,
             headers: {
                 "Content-Type": "application/pdf",
                 "Content-Disposition": `attachment; filename="${filename}"`,
                 "Content-Length": pdfBuffer.length.toString(),
                 "Cache-Control": "no-cache",
             },
         });
     } catch (error) {
         console.error("Erreur lors de la génération du PDF:", error);

         // More detailed error logging
         if (error instanceof Error) {
             console.error("Error name:", error.name);
             console.error("Error message:", error.message);
             console.error("Error stack:", error.stack);
         }

         return handleError(error);
     }
 }
