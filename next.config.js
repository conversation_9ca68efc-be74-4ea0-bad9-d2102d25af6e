/** @type {import('next').NextConfig} */
const nextConfig = {
    // Moved from experimental and corrected
    outputFileTracingRoot: process.cwd(),
    outputFileTracingExcludes: {
        "*": ["node_modules/**", ".next/cache/**"],
    },
    // Improve build performance
    reactStrictMode: true,
    // swcMinify: true, // Removed this line
    // Custom build directory
    distDir: process.env.NODE_ENV === "development" ? ".next-dev" : ".next",
    // The experimental object is now empty and can be removed.
    // If you have other experimental flags, they would remain in an 'experimental' object.
    webpack: (config, { isServer }) => {
        if (isServer) {
            // Handle PDFKit font loading issues
            config.resolve.alias = {
                ...config.resolve.alias,
                canvas: false,
            };

            // Ignore font files that PDFKit tries to load
            config.externals = config.externals || [];
            config.externals.push({
                canvas: "canvas",
                "pdfkit/js/data": "pdfkit/js/data",
            });
        }
        return config;
    },
    experimental: {
        serverComponentsExternalPackages: ["pdfkit"],
    },
};

module.exports = nextConfig;
