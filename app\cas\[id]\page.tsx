// app/cas/[id]/page.tsx
"use client";

import { useEffect, useState, Suspense } from "react";
import { useParams , useSearchParams} from "next/navigation";
import Link from "next/link";
import {
    Cas,
    Problematique,
    Encrage,
    User,
    Secteur,
    Blocage,
    Commune,
    TypePersonne, // Assurez-vous que TypePersonne est importé depuis @prisma/client
} from "@prisma/client";
import { apiClient } from "@/lib/api-client";
import dynamic from "next/dynamic";
import {
    useRegisterDataRefresh,
    useOperationRefresh,
} from "@/app/contexts/DataRefreshContext";
import Skeleton from "@/app/components/Skeleton";
import {
    CheckCircleIcon,
    PlusCircleIcon,
    DocumentArrowDownIcon,
    TrashIcon,
    XCircleIcon,
} from "@heroicons/react/24/outline";
import { Switch } from "@/app/components/switch";
import {
    ResolutionSelect,
    ResolutionBadge,
    type Resolution,
} from "@/app/components/ResolutionSelect";
import {
    CasStatusBadgeWithText,
    determineCasStatus,
    type CasStatus,
} from "@/app/components/CasStatusBadge";
import React from "react";
import CasKMLEditor from "@/app/components/CasKMLEditor";
import {
    WriteAccess,
    DeleteAccess,
    UserRoleBadge,
    ReadOnlyMessage,
    PermissionButton,
} from "@/app/components/RoleBasedAccess";
import { usePermissions } from "@/lib/hooks/usePermissions";

// Correction des imports dynamiques pour retourner le composant par défaut
const LazyLoadingSpinner = dynamic(
    () => import("@/app/components/LoadingSpinner").then((mod) => mod.default),
    { ssr: false, loading: () => <Skeleton height={32} width={32} /> }
);
const LazyFormError = dynamic(
    () => import("@/app/components/FormError").then((mod) => mod.default),
    { ssr: false, loading: () => <Skeleton height={20} width={200} /> }
);
const LazyButton = dynamic(
    () => import("@/app/components/Button").then((mod) => mod.default),
    { ssr: false, loading: () => <Skeleton height={36} width={100} /> }
);
const LazyInput = dynamic(
    () => import("@/app/components/Input").then((mod) => mod.default),
    { ssr: false, loading: () => <Skeleton height={36} width={200} /> }
);
const LazyTextArea = dynamic(
    () => import("@/app/components/TextArea").then((mod) => mod.default),
    { ssr: false, loading: () => <Skeleton height={80} width={200} /> }
);
const LazyModal = dynamic(
    () => import("@/app/components/Modal").then((mod) => mod.default),
    { ssr: false, loading: () => <Skeleton height={200} width={400} /> }
);

// Import du composant de carte personnalisé
const CasMap = dynamic(() => import("@/app/components/CasMap"), {
    ssr: false,
    loading: () => (
        <div className="w-full h-[300px] bg-gray-200 rounded animate-pulse" />
    ),
});

// Étendre les types pour inclure les relations
type CasWithDetails = Cas & {
    problematique?: Problematique & {
        encrage: Encrage;
    };
    user?: Pick<User, "id" | "username" | "role">;
    blocage: (Blocage & { secteur: Secteur })[];
    communes: Commune[];
    date_depot?: string | null;
    geojson?: any; // <-- Ajouté
    kmlData?: any; // <-- Ajouté pour les données KML
    kmlFileName?: string; // <-- Ajouté pour le nom du fichier KML
};

interface BlocageFormData {
    description: string;
    secteurId: string;
    blocageDate: string;
}

// Define a type for the editable Cas form data
interface CasEditFormData {
    nom?: string;
    nif?: string | null;
    nin?: string | null;
    superficie?: number;
    regularisation?: boolean;
    observation?: string | null;
    problematiqueId?: string;
    encrageId?: string; // <-- Ajoute cette ligne
    genre?: TypePersonne;
    date_depot?: string; // Will store as YYYY-MM-DD for the input
    communeIds?: string[]; // Add this for commune IDs
}

export default function CasDetailsPage() {
    const params = useParams();
      const searchParams = useSearchParams();
    const casId = params.id as string;

    // Hooks pour la gestion des rafraîchissements
    const { afterCreate, afterUpdate, afterDelete } = useOperationRefresh();

    // Séparez les states
    const [casMain, setCasMain] = useState<Partial<CasWithDetails> | null>(
        null
    );
    const [blocages, setBlocages] = useState<
        (Blocage & { secteur: Secteur })[]
    >([]);
    const [secteurs, setSecteurs] = useState<Secteur[]>([]);
    const [problematiques, setProblematiques] = useState<Problematique[]>([]); // Ajout pour les problématiques
    const [encrages, setEncrages] = useState<Encrage[]>([]); // Ajout pour les encrages
    const [isLoadingMain, setIsLoadingMain] = useState(true);
    const [isLoadingBlocages, setIsLoadingBlocages] = useState(true);
    const [isLoadingProblematiques, setIsLoadingProblematiques] =
        useState(true);
    const [isLoadingEncrages, setIsLoadingEncrages] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [blocageForm, setBlocageForm] = useState<BlocageFormData>({
        description: "",
        secteurId: "",
        blocageDate: "",
    });
    const [blocageFormData, setBlocageFormData] = useState({
        description: "",
        secteurId: "",
        blocageDate: "", // <-- pour le champ date
        // ajoutez d'autres champs si besoin
    });
    const [isSubmittingBlocage, setIsSubmittingBlocage] = useState(false);
    const [blocageError, setBlocageError] = useState<string | null>(null);
    const [blocageToDeleteId, setBlocageToDeleteId] = useState<string | null>(
        null
    );
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

    // New states for editing Cas details
    const [isEditing, setIsEditing] = useState(false);
    const [editFormData, setEditFormData] = useState<CasEditFormData>({});
    const [isSubmittingEdit, setIsSubmittingEdit] = useState(false);
    const [editError, setEditError] = useState<string | null>(null);
    const [isSolutionModalOpen, setIsSolutionModalOpen] = useState(false);
    const [currentBlocageForSolution, setCurrentBlocageForSolution] =
        useState<Blocage | null>(null);
    const [solutionDate, setSolutionDate] = useState<string>(""); // Stockera la date au format YYYY-MM-DD
    const [solutionDateError, setSolutionDateError] = useState<string | null>(
        null
    );
    const [solutionError, setSolutionError] = useState<string>("");
    const [solutionResolution, setSolutionResolution] =
        useState<Resolution>("ACCEPTE");
    const [solutionDetailResolution, setSolutionDetailResolution] =
        useState<string>("");
    const [isBlocageFormModalOpen, setIsBlocageFormModalOpen] = useState(false); // <-- Add this line
    const [allCommunes, setAllCommunes] = useState<Commune[]>([]);
    const [isLoadingCommunes, setIsLoadingCommunes] = useState(false);
    const [errorCommunes, setErrorCommunes] = useState<string | null>(null);
    const [geojson, setGeojson] = useState<any>(null);
    // États pour la gestion des KML
    const [kmlError, setKmlError] = useState<string | null>(null);
    const [isDownloadingPDF, setIsDownloadingPDF] = useState(false);
    const [pdfError, setPdfError] = useState<string | null>(null);

        const handleDownloadPDF = async () => {
            if (!casMain) return;

            setIsDownloadingPDF(true);
            setPdfError(null);

            try {
                const response = await fetch(`/api/cas/${casId}/pdf`, {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                    },
                });

                if (!response.ok) {
                    let errorMessage = `Erreur HTTP ${response.status}`;

                    try {
                        // Try to get detailed error from response
                        const contentType =
                            response.headers.get("content-type");
                        if (
                            contentType &&
                            contentType.includes("application/json")
                        ) {
                            const errorData = await response.json();
                            errorMessage =
                                errorData.error ||
                                errorData.message ||
                                errorMessage;
                        } else {
                            const errorText = await response.text();
                            if (errorText) {
                                errorMessage = errorText;
                            }
                        }
                    } catch (parseError) {
                        console.warn(
                            "Could not parse error response:",
                            parseError
                        );
                    }

                    // Provide user-friendly messages based on status code
                    switch (response.status) {
                        case 401:
                            errorMessage =
                                "Session expirée. Veuillez vous reconnecter.";
                            break;
                        case 403:
                            errorMessage =
                                "Vous n'avez pas les permissions pour télécharger ce PDF.";
                            break;
                        case 404:
                            errorMessage = "Dossier non trouvé.";
                            break;
                        case 500:
                            errorMessage =
                                "Erreur serveur lors de la génération du PDF. Veuillez réessayer.";
                            break;
                        default:
                            // Keep the detailed error message from API if available
                            break;
                    }

                    throw new Error(errorMessage);
                }

                const blob = await response.blob();

                // Verify we got a PDF blob
                if (blob.type && !blob.type.includes("pdf")) {
                    throw new Error(
                        "Le serveur n'a pas retourné un fichier PDF valide."
                    );
                }

                const url = window.URL.createObjectURL(blob);
                const link = document.createElement("a");
                link.href = url;

                // Generate filename with case ID and timestamp
                const timestamp = new Date().toISOString().split("T")[0];
                const safeName =
                    casMain.nom?.replace(/[^a-zA-Z0-9]/g, "_") || "Dossier";
                const filename = `Fiche_Cas_${casMain.id}_${safeName}_${timestamp}.pdf`;
                link.download = filename;

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error("Erreur lors du téléchargement PDF:", error);
                const errorMessage =
                    error instanceof Error
                        ? error.message
                        : "Erreur inconnue lors de la génération du PDF";
                setPdfError(errorMessage);
            } finally {
                setIsDownloadingPDF(false);
            }
        };
                                       {
                                           pdfError && (
                                               <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                                                   <div className="flex">
                                                       <div className="flex-shrink-0">
                                                           <svg
                                                               className="h-5 w-5 text-red-400"
                                                               viewBox="0 0 20 20"
                                                               fill="currentColor"
                                                           >
                                                               <path
                                                                   fillRule="evenodd"
                                                                   d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                                   clipRule="evenodd"
                                                               />
                                                           </svg>
                                                       </div>
                                                       <div className="ml-3">
                                                           <h3 className="text-sm font-medium text-red-800">
                                                               Erreur de
                                                               génération PDF
                                                           </h3>
                                                           <p className="mt-1 text-sm text-red-700">
                                                               {pdfError}
                                                           </p>
                                                           <button
                                                               onClick={() =>
                                                                   setPdfError(
                                                                       null
                                                                   )
                                                               }
                                                               className="mt-2 text-sm text-red-600 hover:text-red-500 underline"
                                                           >
                                                               Fermer
                                                           </button>
                                                       </div>
                                                   </div>
                                               </div>
                                           );
                                       }

    // Fonctions de gestion des KML
    const handleKMLUpdated = (
        casId: string,
        kmlData: any,
        fileName: string
    ) => {
        if (casMain) {
            setCasMain({
                ...casMain,
                kmlData,
                kmlFileName: fileName,
            });
        }
        setKmlError(null);
    };

    const handleKMLRemoved = (casId: string) => {
        if (casMain) {
            setCasMain({
                ...casMain,
                kmlData: null,
                kmlFileName: undefined,
            });
        }
        setKmlError(null);
    };

    const handleKMLError = (error: string) => {
        setKmlError(error);
        setTimeout(() => setKmlError(null), 5000);
    };

    // Fonction pour mettre à jour le statut du cas basé sur les résolutions des blocages
    const checkAndUpdateCasRegularisationStatus = async (
        blocagesList: (Blocage & { secteur: Secteur })[]
    ) => {
        // Calculer le nouveau statut basé sur les résolutions
        const resolutions = blocagesList.map(
            (b) => (b as any).resolution || "ATTENTE"
        );
        const newRegularisationStatus =
            resolutions.length > 0 && resolutions.every((r) => r === "ACCEPTE");

        if (casMain && casMain.regularisation !== newRegularisationStatus) {
            // Mettre à jour l'état local
            setCasMain((prevCas) =>
                prevCas
                    ? {
                          ...prevCas,
                          regularisation: newRegularisationStatus,
                          blocage: blocagesList, // Mettre à jour aussi les blocages pour le badge de statut
                      }
                    : null
            );

            // Mettre à jour sur le serveur
            try {
                await apiClient.put(`/api/cas/${casId}`, {
                    regularisation: newRegularisationStatus,
                });
            } catch (error) {
                console.error(
                    "Failed to update Cas regularisation status on server:",
                    error
                );
            }
        } else if (casMain) {
            // Même si le statut de régularisation n'a pas changé,
            // mettre à jour les blocages pour le badge de statut
            setCasMain((prevCas) =>
                prevCas
                    ? {
                          ...prevCas,
                          blocage: blocagesList,
                      }
                    : null
            );
        }
    };

    // Populate edit form when cas data is loaded and isEditing is true
    useEffect(() => {
        if (casMain && isEditing) {
            setEditFormData({
                nom: casMain.nom || "",
                nif: casMain.nif,
                nin: casMain.nin,
                superficie: casMain.superficie || undefined,
                regularisation: casMain.regularisation || false,
                observation: casMain.observation,
                problematiqueId: casMain.problematiqueId || "",
                genre: casMain.genre || undefined,
                date_depot: casMain.date_depot
                    ? new Date(casMain.date_depot).toISOString().split("T")[0]
                    : "",
                communeIds: casMain.communes
                    ? casMain.communes.map((c) => String(c.id))
                    : [], // Populate communeId
                encrageId: casMain.problematique?.encrage?.id || "",
            });
        } else if (!isEditing) {
            setEditFormData({}); // Clear form when not editing
            setEditError(null); // Clear edit errors
        }
    }, [casMain, isEditing]);

    // Pré-remplir le geojson à l’ouverture de l’édition
    useEffect(() => {
        if (casMain && isEditing) {
            setGeojson(casMain.geojson || null);
        }
    }, [casMain, isEditing]);

    // Handler for commune checkbox changes
    const handleCommuneChange = (communeId: string) => {
        setEditFormData((prev) => {
            const currentCommuneIds = prev.communeIds || [];
            const newCommuneIds = currentCommuneIds.includes(communeId)
                ? currentCommuneIds.filter((id) => id !== communeId)
                : [...currentCommuneIds, communeId];
            return { ...prev, communeIds: newCommuneIds };
        });
    };
    // 1. Charger les infos principales
    useEffect(() => {
        async function fetchMain() {
            console.time("fetchMain");
            setIsLoadingMain(true);
            try {
                const data = await apiClient.get(
                    `/api/cas/${casId}?mainOnly=true`
                );
                setCasMain(data as Partial<CasWithDetails>);
            } finally {
                setIsLoadingMain(false);
                console.timeEnd("fetchMain");
            }
        }
        fetchMain();
    }, [casId]);

    // 2. Charger le reste en parallèle
    useEffect(() => {
        if (!casMain) return;
        (async () => {
            setIsLoadingBlocages(true);
            setIsLoadingCommunes(true);
            setIsLoadingProblematiques(true);
            setIsLoadingEncrages(true);
            // Ajoutez d'autres isLoading si besoin
            console.time("fetchBlocages");
            console.time("fetchAllCommunes");
            console.time("fetchProblematiques");
            console.time("fetchEncrages");
            try {
                const [
                    blocagesData,
                    communesData,
                    problematiquesData,
                    encragesData,
                ] = await Promise.all([
                    apiClient.get(`/api/cas/${casId}/blocages`),
                    apiClient.get<Commune[]>("/api/communes"),
                    apiClient.get<Problematique[]>("/api/problematiques"),
                    apiClient.get<Encrage[] | { data: Encrage[] }>("/api/encrages"),
                ]);
                setBlocages(blocagesData as (Blocage & { secteur: Secteur })[]);
                setAllCommunes(communesData);
                setProblematiques(problematiquesData);
                const encragesArray = Array.isArray(encragesData)
                    ? encragesData
                    : Array.isArray(encragesData?.data)
                    ? encragesData.data
                    : [];
                setEncrages(encragesArray);
            } catch (err) {
                console.error("Erreur lors du chargement parallèle :", err);
            } finally {
                setIsLoadingBlocages(false);
                setIsLoadingCommunes(false);
                setIsLoadingProblematiques(false);
                setIsLoadingEncrages(false);
                // Ajoutez d'autres setIsLoading si besoin
                console.timeEnd("fetchBlocages");
                console.timeEnd("fetchAllCommunes");
                console.timeEnd("fetchProblematiques");
                console.timeEnd("fetchEncrages");
            }
        })();
    }, [casMain, casId]);

    const fetchAllCommunes = async () => {
        console.time("fetchAllCommunes");
        setIsLoadingCommunes(true);
        setErrorCommunes(null);
        try {
            const data = await apiClient.get<Commune[]>("/api/communes");
            setAllCommunes(data);
        } catch (err: any) {
            console.error("Failed to fetch communes:", err);
            setErrorCommunes(
                err.response?.data?.error ||
                    err.message ||
                    "Failed to fetch communes"
            );
        } finally {
            setIsLoadingCommunes(false);
            console.timeEnd("fetchAllCommunes");
        }
    };
    const fetchProblematiques = async () => {
        console.time("fetchProblematiques");
        try {
            const data = await apiClient.get<Problematique[]>(
                "/api/problematiques"
            );
            setProblematiques(data);
        } catch (err: any) {
            console.error("Failed to fetch problematiques:", err);
            // Gérer l'erreur (par exemple, afficher un message à l'utilisateur)
        } finally {
            console.timeEnd("fetchProblematiques");
        }
    };

    const fetchEncrages = async () => {
        console.time("fetchEncrages");
        try {
            const data = await apiClient.get<Encrage[]>("/api/encrages");
            setEncrages(data);
        } catch (err: any) {
            console.error("Failed to fetch encrages:", err);
            // Gérer l'erreur
        } finally {
            console.timeEnd("fetchEncrages");
        }
    };

    async function handleToggleRegularisation(
        blocageId: string,
        currentStatus: boolean
    ) {
        const blocageToUpdate = blocages.find((b) => b.id === blocageId);
        if (!blocageToUpdate) return;

        // Toujours ouvrir le modal pour permettre de choisir la résolution
        setCurrentBlocageForSolution(blocageToUpdate);
        setSolutionDate(
            blocageToUpdate.solution
                ? new Date(blocageToUpdate.solution).toISOString().split("T")[0]
                : ""
        );

        // Si le blocage est déjà régularisé, préremplir avec les valeurs existantes
        if (currentStatus) {
            setSolutionResolution(
                (blocageToUpdate as any).resolution || "ACCEPTE"
            );
            setSolutionDetailResolution(
                (blocageToUpdate as any).detail_resolution || ""
            );
        } else {
            // Nouveau blocage, valeurs par défaut
            setSolutionResolution("ACCEPTE");
            setSolutionDetailResolution("");
        }

        setSolutionError("");
        setIsSolutionModalOpen(true);
    }
    function closeSolutionModal() {
        setIsSolutionModalOpen(false);
        setCurrentBlocageForSolution(null);
        setSolutionDate("");
        setSolutionResolution("ACCEPTE");
        setSolutionDetailResolution("");
        setSolutionError("");
    }
    const handleSolutionSubmit = async (
        e?: React.FormEvent<HTMLFormElement>
    ) => {
        if (e) e.preventDefault();

        if (!currentBlocageForSolution || !solutionDate) {
            setSolutionDateError("Solution date is required");
            return;
        }

        setSolutionDateError(null);

        try {
            const response = await apiClient.put(
                `/api/blocages/${currentBlocageForSolution.id}`,
                {
                    regularise: solutionResolution === "ACCEPTE",
                    solution: new Date(solutionDate).toISOString(),
                    resolution: solutionResolution,
                    detail_resolution: solutionDetailResolution || null,
                }
            );

            if (!response) {
                throw new Error("Failed to submit solution date");
            }

            // Mettre à jour le blocage dans l'état local
            const updatedBlocages = blocages.map((b) =>
                b.id === currentBlocageForSolution.id
                    ? {
                          ...b,
                          regularise: solutionResolution === "ACCEPTE",
                          solution: new Date(solutionDate),
                          resolution: solutionResolution,
                          detail_resolution: solutionDetailResolution || null,
                      }
                    : b
            );
            setBlocages(updatedBlocages);

            // Mettre à jour le statut du cas basé sur les nouvelles résolutions
            await checkAndUpdateCasRegularisationStatus(updatedBlocages);

            // Déclencher le rafraîchissement des données dans toute l'application
            await afterUpdate("blocage");

            setIsSolutionModalOpen(false);
            setSolutionDate("");
            setSolutionResolution("ACCEPTE");
            setSolutionDetailResolution("");
            setCurrentBlocageForSolution(null);
        } catch (error: any) {
            console.error("Error submitting solution date:", error);
            setSolutionDateError(error.message || "Failed to submit solution");
        }
    };

    const fetchCasDetails = async () => {
        setIsLoadingMain(true);
        setError(null);

        try {
            const casData = await apiClient.get<CasWithDetails>(
                `/api/cas/${casId}`
            );
            setCasMain(casData);
            const updatedBlocages = casData.blocage || [];
            setBlocages(updatedBlocages);
            // Call checkAndUpdateCasRegularisationStatus with the newly fetched blocages
            if (casData) {
                // Ensure casData is not null
                await checkAndUpdateCasRegularisationStatus(updatedBlocages);
            }
        } catch (err: any) {
            console.error("Failed to fetch case details:", err);
            setError(
                err.response?.data?.error ||
                    err.message ||
                    "Failed to fetch case details"
            );
        }

        setIsLoadingMain(false);
    };

    const fetchSecteurs = async () => {
        try {
            const secteursData = await apiClient.get<Secteur[]>(
                "/api/secteurs"
            );
            setSecteurs(secteursData);
        } catch (err: any) {
            console.error("Failed to fetch sectors:", err);
            // Handle error silently
        }
    };

    const handleBlocageFormChange = (
        e: React.ChangeEvent<
            HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
        >
    ) => {
        setBlocageForm({
            ...blocageForm,
            [e.target.name]: e.target.value,
        });
    };

    const handleAddBlocage = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!blocageForm.description || !blocageForm.secteurId) {
            setBlocageError("La description et le secteur sont requis.");
            return;
        }
        setIsSubmittingBlocage(true);
        setBlocageError(null);
        try {
            const newBlocage = await apiClient.post<
                Blocage & { secteur: Secteur }
            >("/api/blocages", {
                ...blocageForm,
                casId: casId, // S'assurer que casId est bien envoyé
                blocage: blocageForm.blocageDate
                    ? new Date(blocageForm.blocageDate)
                    : null,
            });
            // Recharge les blocages via le nouveau endpoint scoped
            const data = await apiClient.get<
                (Blocage & { secteur: Secteur })[]
            >(`/api/cas/${casId}/blocages`);
            setBlocages(data);

            // Mettre à jour le statut du cas basé sur les nouvelles résolutions
            await checkAndUpdateCasRegularisationStatus(data);
            setBlocageForm({
                description: "",
                secteurId: "",
                blocageDate: "",
            });
            setIsBlocageFormModalOpen(false);

            // Déclencher le rafraîchissement via le système centralisé
            await afterCreate("blocage");
        } catch (err: any) {
            setBlocageError(
                err.message || "Erreur lors de l'ajout du blocage."
            );
        } finally {
            setIsSubmittingBlocage(false);
        }
    };

    const initiateDeleteBlocage = (id: string) => {
        setBlocageToDeleteId(id);
        setIsDeleteModalOpen(true);
    };

    const confirmDeleteBlocage = async () => {
        if (!blocageToDeleteId) return;
        setError(null);

        try {
            await apiClient.delete(`/api/blocages/${blocageToDeleteId}`);
            const updatedBlocages = blocages.filter(
                (b) => b.id !== blocageToDeleteId
            );
            setBlocages(updatedBlocages);
            await checkAndUpdateCasRegularisationStatus(updatedBlocages); // Add this line
            setBlocageToDeleteId(null);
            setIsDeleteModalOpen(false);

            // Déclencher le rafraîchissement via le système centralisé
            await afterDelete("blocage");
        } catch (err: any) {
            console.error("Error deleting blocage:", err);
            setError(
                err.response?.data?.error ||
                    err.message ||
                    "Error deleting blocage."
            );
            setBlocageToDeleteId(null);
            setIsDeleteModalOpen(false);
        }
    };

    const cancelDeleteBlocage = () => {
        setBlocageToDeleteId(null);
        setIsDeleteModalOpen(false);
    };

    // Handlers for editing Cas details
    const handleEditFormChange = (
        e: React.ChangeEvent<
            HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
        >
    ) => {
        const { name, value, type } = e.target;
        let processedValue: any = value;

        if (type === "number") {
            processedValue = value === "" ? undefined : parseFloat(value);
        }
        // For checkbox, specific handling if you use a standard HTML input
        if (e.target.type === "checkbox") {
            processedValue = (e.target as HTMLInputElement).checked;
        }

        setEditFormData((prev) => ({
            ...prev,
            [name]: processedValue,
        }));
    };

    const handleEditSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsSubmittingEdit(true);
        setEditError(null);
        try {
            // Convertir les IDs de communes en objets complets
            const selectedCommunes = editFormData.communeIds
                ? allCommunes
                      .filter((commune) =>
                          editFormData.communeIds!.includes(String(commune.id))
                      )
                      .map((commune) => ({
                          nom: commune.nom,
                          wilayaId: commune.wilayaId,
                      }))
                : [];

            const { communeIds, ...otherFormData } = editFormData;

            const dataToSend = {
                ...otherFormData,
                communes: selectedCommunes,
                geojson: geojson || null,
            };

            console.log("Données envoyées pour la modification:", dataToSend);

            const updatedCas = await apiClient.put(
                `/api/cas/${casId}`,
                dataToSend
            );
            setIsEditing(false);

            // Déclencher le rafraîchissement via le système centralisé
            await afterUpdate("cas");
        } catch (err: any) {
            console.error("Erreur lors de la modification:", err);
            setEditError("Erreur lors de la modification du cas.");
        }
        setIsSubmittingEdit(false);
    };

    // Charger les communes, problématiques et encrages au montage
    useEffect(() => {
        fetchAllCommunes();
        fetchProblematiques();
        fetchEncrages();
    }, []);

    // Enregistrer les callbacks de rafraîchissement
    useRegisterDataRefresh("cas-details", fetchCasDetails, [casId]);
    useRegisterDataRefresh("communes-details", fetchAllCommunes, []);
    useRegisterDataRefresh("problematiques-details", fetchProblematiques, []);
    useRegisterDataRefresh("encrages-details", fetchEncrages, []);

    // Charger les secteurs au montage pour le formulaire de blocage
    useEffect(() => {
        fetchSecteurs();
    }, []);

    // Ajout d'un état pour l'utilisateur courant
    const [currentUser, setCurrentUser] = useState<{
        id: string;
        username: string;
        role: string;
        wilayaId?: number;
    } | null>(null);

    // Permissions hook for role-based access control
    const { canWrite, canDelete, canUploadFiles, isViewer } = usePermissions();
    useEffect(() => {
        async function loadCurrentUser() {
            try {
                const response = await apiClient.get<{
                    id: string;
                    username: string;
                    role: string;
                    wilayaId?: number;
                }>("/api/auth/me");
                setCurrentUser(response);
            } catch (error) {
                setCurrentUser(null);
            }
        }
        loadCurrentUser();
    }, []);

    if (error) {
        return (
            <div className="container mx-auto p-1 text-center">
                <LazyFormError message={error} />
                <LazyButton onClick={fetchCasDetails} className="mt-4">
                    Réessayer
                </LazyButton>
            </div>
        );
    }

    // Remove the global isLoadingMain loader so the page always renders
    // if (isLoadingMain) {
    //     return <LazyLoadingSpinner />;
    // }

    // Main component render

    // Prevent rendering sections if casMain is null (still loading)
    if (!casMain) {
        return (
            <div className="container mx-auto p-1 text-center">
                {isLoadingMain ? (
                    <LazyLoadingSpinner />
                ) : (
                    <>
                        <p>Cas non trouvé.</p>
                        <Link
                            href="/cas"
                            className="text-purple-600 hover:underline"
                        >
                            Retour à la liste des cas
                        </Link>
                    </>
                )}
            </div>
        );
    }

    // BlocageRow props type
    type BlocageRowProps = {
        blocage: Blocage & { secteur: Secteur };
        blocageToDeleteId: string | null;
        onDelete: (id: string) => void;
        onCancelDelete: () => void;
        onConfirmDelete: () => void;
        onToggleRegularisation: (
            id: string,
            currentStatus: boolean
        ) => void | Promise<void>;
        isDeleteModalOpen: boolean;
        setIsDeleteModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
    };

    // Memoized BlocageRow component
    const BlocageRow: React.FC<BlocageRowProps> = React.memo(
        function BlocageRow({
            blocage,
            blocageToDeleteId,
            onDelete,
            onCancelDelete,
            onConfirmDelete,
            onToggleRegularisation,
            isDeleteModalOpen,
            setIsDeleteModalOpen,
        }: BlocageRowProps) {
            return (
                <div className="p-4 bg-white rounded-md shadow border border-gray-200">
                    <div className="flex justify-between items-start mb-2">
                        <div>
                            <p className="text-sm text-gray-800 font-medium">
                                {blocage.description}
                            </p>
                            <p className="text-xs text-gray-600">
                                Secteur: {blocage.secteur?.nom || "N/A"}
                            </p>
                            <div className="mt-2 flex items-center gap-2">
                                <span className="text-xs text-gray-500">
                                    Résolution:
                                </span>
                                <ResolutionBadge
                                    resolution={
                                        ((blocage as any).resolution ||
                                            "ATTENTE") as Resolution
                                    }
                                    className="text-xs"
                                />
                            </div>
                            {(blocage as any).detail_resolution && (
                                <p className="text-xs text-gray-600 mt-1 italic">
                                    {(blocage as any).detail_resolution}
                                </p>
                            )}
                        </div>
                        <div className="flex space-x-2 flex-shrink-0">
                            {blocageToDeleteId === blocage.id ? (
                                <>
                                    <LazyButton
                                        variant="primary"
                                        size="icon"
                                        onClick={onConfirmDelete}
                                        className="p-1.5 rounded-full bg-green-100 hover:bg-green-200 text-green-700"
                                        aria-label="Confirmer la suppression"
                                    >
                                        <CheckCircleIcon className="w-5 h-5" />
                                    </LazyButton>
                                    <LazyButton
                                        variant="secondary"
                                        size="icon"
                                        onClick={() =>
                                            setIsDeleteModalOpen(false)
                                        }
                                        className="p-1.5 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700"
                                        aria-label="Annuler la suppression"
                                    >
                                        <XCircleIcon className="w-5 h-5" />
                                    </LazyButton>
                                </>
                            ) : (
                                <DeleteAccess>
                                    <LazyButton
                                        variant="destructive"
                                        size="icon"
                                        onClick={() => onDelete(blocage.id)}
                                        className="ml-2 flex-shrink-0 p-1.5 rounded-full hover:bg-red-100 hover:text-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                        aria-label="Supprimer le blocage"
                                        title={
                                            !canDelete
                                                ? "Vous n'avez pas les permissions pour supprimer des blocages"
                                                : undefined
                                        }
                                        disabled={!canDelete}
                                    >
                                        <TrashIcon className="w-5 h-5" />
                                    </LazyButton>
                                </DeleteAccess>
                            )}
                        </div>
                    </div>
                    <div className="flex items-center justify-between mt-2 pt-2">
                        <div className="flex items-center">
                            <WriteAccess
                                fallback={
                                    <div className="inline-flex h-6 w-11 items-center rounded-full bg-gray-200 border border-gray-300 opacity-50 cursor-not-allowed">
                                        <span
                                            className={`inline-block h-4 w-4 rounded-full bg-white shadow transform transition-transform ${
                                                blocage.regularise
                                                    ? "translate-x-6"
                                                    : "translate-x-1"
                                            }`}
                                        />
                                    </div>
                                }
                            >
                                <Switch
                                    checked={blocage.regularise}
                                    onChange={() =>
                                        onToggleRegularisation(
                                            blocage.id,
                                            blocage.regularise
                                        )
                                    }
                                    disabled={!canWrite}
                                    className={
                                        (blocage as any).resolution ===
                                        "ACCEPTE"
                                            ? "bg-green-600 border-green-600 focus:ring-green-500"
                                            : (blocage as any).resolution ===
                                              "REJETE"
                                            ? "bg-red-600 border-red-600 focus:ring-red-500"
                                            : (blocage as any).resolution ===
                                              "AJOURNE"
                                            ? "bg-orange-600 border-orange-600 focus:ring-orange-500"
                                            : "bg-gray-400 border-gray-400 focus:ring-gray-500" +
                                              " inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border"
                                    }
                                />
                            </WriteAccess>
                            <span
                                className={`ml-2 text-sm font-medium cursor-pointer ${
                                    (blocage as any).resolution === "ACCEPTE"
                                        ? "text-green-700"
                                        : (blocage as any).resolution ===
                                          "REJETE"
                                        ? "text-red-700"
                                        : (blocage as any).resolution ===
                                          "AJOURNE"
                                        ? "text-orange-700"
                                        : "text-gray-600"
                                }`}
                                onClick={() =>
                                    onToggleRegularisation(
                                        blocage.id,
                                        blocage.regularise
                                    )
                                }
                            >
                                {(blocage as any).resolution === "ACCEPTE"
                                    ? "Accepté"
                                    : (blocage as any).resolution === "REJETE"
                                    ? "Rejeté"
                                    : (blocage as any).resolution === "AJOURNE"
                                    ? "Ajourné"
                                    : "En attente"}
                            </span>
                            {blocage.regularise && blocage.solution && (
                                <span className="ml-3 text-sm text-green-700 ">
                                    Solutionné le:{" "}
                                    {new Date(
                                        blocage.solution
                                    ).toLocaleDateString("fr-CA")}
                                </span>
                            )}
                        </div>
                        <div className="text-xs text-gray-400">
                            {blocage.blocage && (
                                <p className="text-xs text-gray-400">
                                    Ajouté le:{" "}
                                    {new Date(
                                        blocage.blocage
                                    ).toLocaleDateString("fr-CA")}
                                </p>
                            )}
                        </div>
                    </div>
                </div>
            );
        }
    );

    // Memoized status badge component
    const CasStatusBadgeLocal: React.FC<{ casData: any }> = React.memo(
        function CasStatusBadgeLocal({ casData }) {
            const status = determineCasStatus(casData?.blocage || []);
            return (
                <div className="ml-4">
                    <CasStatusBadgeWithText status={status} />
                </div>
            );
        }
    );

    return (
        <div className="container mx-auto p-2 md:p-2 bg-gray-50 min-h-screen">
            {/* 1. Add a global loading overlay for main actions */}
            {isLoadingMain && (
                <div className="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50 transition-opacity duration-300 animate-fade-in">
                    <LazyLoadingSpinner />
                </div>
            )}

            <LazyModal
                isOpen={isBlocageFormModalOpen}
                onClose={() => setIsBlocageFormModalOpen(false)}
                title="Ajouter une Nouvelle Contrainte"
            >
                <form onSubmit={handleAddBlocage} className="space-y-4">
                    <div>
                        <label
                            htmlFor="description"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Description de la contrainte
                        </label>
                        <LazyTextArea
                            name="description"
                            id="description"
                            value={blocageForm.description}
                            onChange={handleBlocageFormChange}
                            required
                            rows={3}
                        />
                    </div>
                    <div>
                        <label
                            htmlFor="secteurId"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Secteur Concerné
                        </label>
                        <select
                            name="secteurId"
                            id="secteurId"
                            value={blocageForm.secteurId}
                            onChange={handleBlocageFormChange}
                            required
                            className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                        >
                            <option value="">Sélectionner un secteur</option>
                            {secteurs.map((secteur) => (
                                <option key={secteur.id} value={secteur.id}>
                                    {secteur.nom}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div>
                        <label
                            htmlFor="blocageDate"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Date de blocage
                        </label>
                        <LazyInput
                            type="date"
                            name="blocageDate"
                            id="blocageDate"
                            value={blocageForm.blocageDate}
                            onChange={handleBlocageFormChange}
                            className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                        />
                    </div>

                    {blocageError && <LazyFormError message={blocageError} />}
                    <div className="flex justify-end space-x-3">
                        <LazyButton
                            type="button"
                            variant="secondary"
                            onClick={() => setIsBlocageFormModalOpen(false)}
                        >
                            Annuler
                        </LazyButton>
                        <WriteAccess>
                            <LazyButton
                                type="submit"
                                disabled={isSubmittingBlocage || !canWrite}
                                title={
                                    !canWrite
                                        ? "Vous n'avez pas les permissions pour ajouter des blocages"
                                        : undefined
                                }
                            >
                                {isSubmittingBlocage ? (
                                    <LazyLoadingSpinner />
                                ) : (
                                    "Ajouter"
                                )}
                            </LazyButton>
                        </WriteAccess>
                    </div>
                </form>
            </LazyModal>

            {isEditing ? (
                <form
                    onSubmit={handleEditSubmit}
                    className="bg-white shadow-lg rounded-xl p-6 border border-gray-200 mb-8"
                >
                    <h2 className="text-2xl font-bold text-gray-800 mb-6">
                        Modifier le Cas
                    </h2>
                    {/* Commune Selection */}
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Communes
                        </label>
                        {isLoadingCommunes && <LazyLoadingSpinner />}
                        {errorCommunes && (
                            <LazyFormError message={errorCommunes} />
                        )}
                        {!isLoadingCommunes &&
                            !errorCommunes &&
                            allCommunes.length > 0 && (
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-60 overflow-y-auto p-2 border rounded">
                                    {allCommunes.map((commune) => (
                                        <div
                                            key={commune.id}
                                            className="flex items-center"
                                        >
                                            <input
                                                type="checkbox"
                                                id={`commune-${commune.id}`}
                                                name="communes"
                                                value={commune.id}
                                                checked={
                                                    editFormData.communeIds?.includes(
                                                        String(commune.id)
                                                    ) || false
                                                }
                                                onChange={() =>
                                                    handleCommuneChange(
                                                        String(commune.id)
                                                    )
                                                }
                                                className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                                            />
                                            <label
                                                htmlFor={`commune-${commune.id}`}
                                                className="ml-2 block text-sm text-gray-900"
                                            >
                                                {commune.nom}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            )}
                        {!isLoadingCommunes &&
                            !errorCommunes &&
                            allCommunes.length === 0 && (
                                <span className="text-sm text-gray-500">
                                    Aucune commune disponible.
                                </span>
                            )}
                    </div>
                    {editError && <LazyFormError message={editError} />}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                        <div>
                            <label
                                htmlFor="nom_edit"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Nom du Cas
                            </label>
                            <LazyInput
                                type="text"
                                name="nom"
                                id="nom_edit"
                                value={editFormData.nom || ""}
                                onChange={handleEditFormChange}
                                required
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="date_depot_edit"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Date dépôt dossier
                            </label>
                            <LazyInput
                                type="date"
                                name="date_depot"
                                id="date_depot_edit"
                                value={editFormData.date_depot || ""}
                                onChange={handleEditFormChange}
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="nif_edit"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                NIF
                            </label>
                            <LazyInput
                                type="text"
                                name="nif"
                                id="nif_edit"
                                value={editFormData.nif || ""}
                                onChange={handleEditFormChange}
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="nin_edit"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                NIN
                            </label>
                            <LazyInput
                                type="text"
                                name="nin"
                                id="nin_edit"
                                value={editFormData.nin || ""}
                                onChange={handleEditFormChange}
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="superficie_edit"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Superficie (Ha)
                            </label>
                            <LazyInput
                                type="number"
                                name="superficie"
                                id="superficie_edit"
                                value={editFormData.superficie || ""}
                                onChange={handleEditFormChange}
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="genre_edit"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Genre
                            </label>
                            <select
                                name="genre"
                                id="genre_edit"
                                value={editFormData.genre || ""}
                                onChange={handleEditFormChange}
                                className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                            >
                                <option value="">Sélectionner un genre</option>
                                <option value={TypePersonne.PERSONNE_PHYSIQUE}>
                                    Personne Physique
                                </option>
                                <option value={TypePersonne.PERSONNE_MORALE}>
                                    Personne Morale
                                </option>
                            </select>
                        </div>
                        {/* Champ Problématique */}
                        <div>
                            <label
                                htmlFor="problematiqueId_edit"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Problématique
                            </label>
                            <select
                                name="problematiqueId"
                                id="problematiqueId_edit"
                                value={editFormData.problematiqueId || ""}
                                onChange={handleEditFormChange}
                                className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                            >
                                <option value="">
                                    Sélectionner une problématique
                                </option>
                                {problematiques
                                    .filter(
                                        (prob) =>
                                            prob.encrageId ===
                                            editFormData.encrageId
                                    )
                                    .map((prob) => (
                                        <option key={prob.id} value={prob.id}>
                                            {prob.problematique}
                                        </option>
                                    ))}
                            </select>
                        </div>
                        {/* Champ Encrage (Si l'encrage est directement lié au Cas et non seulement via la problématique) */}
                        {/* Si l'encrage est déterminé par la problématique, ce champ pourrait ne pas être nécessaire ici */}
                        {/* ou il faudrait filtrer les encrages en fonction de la problématique sélectionnée */}
                        {/* Pour l'instant, je vais supposer que vous voulez pouvoir sélectionner un encrage indépendamment */}
                        {/* ou que la problématique sélectionnée a un encrageId que vous utiliserez au backend */}
                        <div>
                            <label
                                htmlFor="encrageId_edit"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Encrage
                            </label>
                            <select
                                name="encrageId"
                                id="encrageId_edit"
                                value={editFormData.encrageId || ""}
                                onChange={handleEditFormChange}
                                className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                            >
                                <option value="" disabled>
                                    Sélectionner un encrage
                                </option>
                                {(Array.isArray(encrages) ? encrages : []).map(
                                    (encrage) => (
                                        <option
                                            key={encrage.id}
                                            value={encrage.id}
                                        >
                                            {encrage.nom}
                                        </option>
                                    )
                                )}
                            </select>
                        </div>
                    </div>
                    <div className="mb-4">
                        <label
                            htmlFor="observation_edit"
                            className="block text-sm font-medium text-gray-700 mb-1"
                        >
                            Observation
                        </label>
                        <LazyTextArea
                            name="observation"
                            id="observation_edit"
                            value={editFormData.observation || ""}
                            onChange={handleEditFormChange}
                            rows={3}
                        />
                    </div>
                    {/* Champ GeoJSON (KML) */}
                    {/* {isEditing && (
                        <div>
                            <GeoJsonDropzone
                                value={geojson}
                                onChange={setGeojson}
                            />
                        </div>
                    )} */}

                    <div className="mt-6 flex justify-end space-x-3">
                        <LazyButton
                            type="button"
                            onClick={() => setIsEditing(false)}
                            variant="secondary"
                        >
                            Annuler
                        </LazyButton>
                        <WriteAccess>
                            <LazyButton
                                type="submit"
                                isLoading={isSubmittingEdit}
                                disabled={isSubmittingEdit || !canWrite}
                                title={
                                    !canWrite
                                        ? "Vous n'avez pas les permissions pour modifier ce dossier"
                                        : undefined
                                }
                            >
                                Enregistrer
                            </LazyButton>
                        </WriteAccess>
                    </div>
                </form>
            ) : (
                <div className="bg-white shadow-lg rounded-xl overflow-hidden border border-gray-100">
                    <div className="px-6 py-4 border-b border-gray-100 flex flex-col md:flex-row md:items-center md:justify-between gap-4 ">
                        {isLoadingMain ? (
                            <Skeleton height={48} width="100%" />
                        ) : (
                            <>
                                <div className="flex items-center gap-4 min-w-0">
                                    <h1 className="text-3xl font-bold text-gray-900 truncate">
                                        {casMain.nom}
                                    </h1>
                                    <CasStatusBadgeLocal casData={casMain} />
                                </div>
                            </>
                        )}
                    </div>
                    <div className="flex items-center justify-end gap-4 min-w-0">
                        <div className="flex space-x-2 flex-shrink-0">
                            <Link
                                href={`/cas/${casId}/cartographie`}
                                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
                            >
                                <svg
                                    className="w-4 h-4 mr-2"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                                    />
                                </svg>
                                Cartographie
                            </Link>

                            <LazyButton
                                onClick={handleDownloadPDF}
                                variant="secondary"
                                className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white"
                                isLoading={isDownloadingPDF}
                                disabled={isDownloadingPDF}
                                title="Télécharger la fiche PDF du dossier"
                            >
                                <DocumentArrowDownIcon className="h-5 w-5" />
                                <span>
                                    {isDownloadingPDF
                                        ? "Génération..."
                                        : "Fiche PDF"}
                                </span>
                            </LazyButton>

                            <WriteAccess>
                                <LazyButton
                                    onClick={() => setIsEditing(!isEditing)}
                                    variant={
                                        isEditing ? "secondary" : "primary"
                                    }
                                    title={
                                        !canWrite
                                            ? "Vous n'avez pas les permissions pour modifier ce dossier"
                                            : undefined
                                    }
                                    disabled={!canWrite}
                                >
                                    {isEditing
                                        ? "Annuler la Modification"
                                        : "Modifier le Dossier"}
                                </LazyButton>
                            </WriteAccess>

                            <WriteAccess>
                                <LazyButton
                                    onClick={() =>
                                        setIsBlocageFormModalOpen(true)
                                    }
                                    variant="primary"
                                    className="flex items-center space-x-2"
                                    title={
                                        !canWrite
                                            ? "Vous n'avez pas les permissions pour ajouter des blocages"
                                            : undefined
                                    }
                                    disabled={!canWrite}
                                >
                                    <PlusCircleIcon className="h-5 w-5" />
                                    <span>Ajouter une Contrainte</span>
                                </LazyButton>
                            </WriteAccess>
                        </div>
                    </div>

                    {/* Read-only message for VIEWER users */}
                    {isViewer && (
                        <div className="p-3">
                            <ReadOnlyMessage message="Vous êtes en mode lecture seule. Vous pouvez consulter toutes les informations du dossier mais ne pouvez pas les modifier." />
                        </div>
                    )}

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-3">
                        <div className="space-y-3">
                            <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                                    <svg
                                        className="w-5 h-5 mr-2 text-gray-500"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path d="M10 9a3 3 0 100-6 3 3 0 000 6zM6 8a2 2 0 11-4 0 2 2 0 014 0zM1.49 15.326a.78.78 0 01-.358-.442 3 3 0 014.308-3.516 6.484 6.484 0 00-1.905 3.959c-.023.222-.014.442.025.654a4.97 4.97 0 01-2.07-.655zM16.44 15.98a4.97 4.97 0 002.07-.654.78.78 0 00.357-.442 3 3 0 00-4.308-3.517 6.484 6.484 0 011.907 3.96 2.32 2.32 0 01-.026.654zM18 8a2 2 0 11-4 0 2 2 0 014 0zM5.304 16.19a.844.844 0 01-.277-.71 5 5 0 019.947 0 .843.843 0 01-.277.71A6.975 6.975 0 0110 18a6.974 6.974 0 01-4.696-1.81z" />
                                    </svg>
                                    Informations Générales
                                </h2>
                                {isLoadingMain ? (
                                    <Skeleton
                                        height={120}
                                        width="100%"
                                        className="transition-opacity duration-300"
                                    />
                                ) : (
                                    <dl className="grid grid-cols-1 gap-4">
                                        <div className="flex justify-between py-3 border-b border-gray-100">
                                            <dt className="text-sm font-medium text-gray-500">
                                                Identifiant
                                            </dt>
                                            <dd className="text-sm text-gray-900">
                                                {casMain.nif
                                                    ? `NIF: ${casMain.nif}`
                                                    : casMain.nin
                                                    ? `NIN: ${casMain.nin}`
                                                    : "Non spécifié"}
                                            </dd>
                                        </div>
                                        <div className="flex justify-between py-3 border-b border-gray-100">
                                            <dt className="text-sm font-medium text-gray-500">
                                                Genre
                                            </dt>
                                            <dd className="text-sm text-gray-900">
                                                {casMain.genre ===
                                                TypePersonne.PERSONNE_PHYSIQUE
                                                    ? "Personne Physique"
                                                    : casMain.genre ===
                                                      TypePersonne.PERSONNE_MORALE
                                                    ? "Personne Morale"
                                                    : "Non spécifié"}
                                            </dd>
                                        </div>
                                        <div className="flex justify-between py-3 border-b border-gray-100">
                                            <dt className="text-sm font-medium text-gray-500">
                                                Commune(s)
                                            </dt>
                                            <dd className="text-sm text-gray-900">
                                                {casMain.communes &&
                                                casMain.communes.length > 0
                                                    ? casMain.communes
                                                          .map((c) => c.nom)
                                                          .join(", ")
                                                    : "Non spécifiée"}
                                            </dd>
                                        </div>
                                        <div className="flex justify-between py-3 border-b border-gray-100">
                                            <dt className="text-sm font-medium text-gray-500">
                                                Superficie
                                            </dt>
                                            <dd className="text-sm text-gray-900">
                                                {casMain.superficie
                                                    ? `${casMain.superficie} Ha`
                                                    : "Non spécifiée"}
                                            </dd>
                                        </div>
                                        <div className="flex justify-between py-3">
                                            <dt className="text-sm font-medium text-gray-500">
                                                Date dépôt dossier
                                            </dt>
                                            <dd className="text-sm text-gray-900">
                                                {casMain.date_depot
                                                    ? new Date(
                                                          casMain.date_depot
                                                      ).toLocaleDateString(
                                                          "fr-CA"
                                                      )
                                                    : "Non spécifiée"}
                                            </dd>
                                        </div>
                                    </dl>
                                )}
                            </div>

                            <div className="bg-yellow-50 rounded-lg p-6">
                                <h2 className="text-xl font-semibold text-gray-900 flex items-center mb-4">
                                    <svg
                                        className="w-5 h-5 mr-2 text-yellow-600"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zm0 16a3 3 0 01-3-3h6a3 3 0 01-3 3z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                    Observations
                                </h2>
                                {isLoadingMain ? (
                                    <Skeleton
                                        height={40}
                                        width="100%"
                                        className="transition-opacity duration-300"
                                    />
                                ) : (
                                    <p className="text-sm text-gray-700 whitespace-pre-wrap">
                                        {casMain.observation ||
                                            "Aucune observation"}
                                    </p>
                                )}
                            </div>
                        </div>

                        <div className="space-y-6">
                            <div className="bg-blue-50 rounded-lg p-6">
                                <h2 className="text-xl font-semibold text-gray-900 flex items-center mb-4">
                                    <svg
                                        className="w-5 h-5 mr-2 text-blue-600"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                                    </svg>
                                    Problématique Associée
                                </h2>
                                {isLoadingMain ? (
                                    <Skeleton
                                        height={80}
                                        width="100%"
                                        className="transition-opacity duration-300"
                                    />
                                ) : (
                                    <dl className="space-y-4">
                                        <div>
                                            <dt className="text-sm font-medium text-blue-700">
                                                Problématique
                                            </dt>
                                            <dd className="mt-1 text-sm text-gray-900">
                                                {
                                                    casMain.problematique
                                                        ?.problematique
                                                }
                                            </dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-blue-700">
                                                Encrage Juridique
                                            </dt>
                                            <dd className="mt-1 text-sm text-gray-900">
                                                {
                                                    casMain.problematique
                                                        ?.encrage.nom
                                                }
                                            </dd>
                                        </div>
                                    </dl>
                                )}
                            </div>

                            <div className="bg-green-50 rounded-lg p-6">
                                <h2 className="text-xl font-semibold text-gray-900 flex items-center mb-4">
                                    <svg
                                        className="w-5 h-5 mr-2 text-green-600"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                    Informations du créateur
                                </h2>
                                {isLoadingMain ? (
                                    <Skeleton
                                        height={60}
                                        width="100%"
                                        className="transition-opacity duration-300"
                                    />
                                ) : (
                                    <dl className="space-y-4">
                                        <div>
                                            <dt className="text-sm font-medium text-green-700">
                                                Nom d'utilisateur
                                            </dt>
                                            <dd className="mt-1 text-sm text-gray-900">
                                                {casMain.user?.username}
                                            </dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-green-700">
                                                Rôle
                                            </dt>
                                            <dd className="mt-1 text-sm text-gray-900">
                                                {casMain.user?.role}
                                            </dd>
                                        </div>
                                    </dl>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Blocages section */}
            <div className="mt-2 bg-white shadow-lg rounded-xl overflow-hidden border border-gray-100">
                <div className="px-6 py-5 border-b border-gray-100 bg-purple-50">
                    <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                        <svg
                            className="w-6 h-6 mr-2 text-purple-600"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path d="M3.505 2.365A41.369 41.369 0 009 2c1.863 0 3.697.124 5.495.365 1.247.167 2.18 1.108 2.435 2.268a4.45 4.45 0 00-.577-.069 43.141 43.141 0 00-4.706 0C9.229 4.696 7.5 6.727 7.5 8.998v2.24c0 1.413.67 2.735 1.76 3.562l-2.98 2.98A.75.75 0 015 17.25v-3.443c-.501-.048-1-.106-1.495-.172C2.033 13.438 1 12.162 1 10.72V5.28c0-1.441 1.033-2.717 2.505-2.914z" />
                            <path d="M14 6c-.762 0-1.52.02-2.271.062C10.157 6.148 9 7.472 9 8.998v2.24c0 1.519 1.147 2.839 2.71 2.935.214.013.428.024.642.034.2.009.385.09.518.224l2.35 2.35a.75.75 0 001.28-.531v-2.07c1.453-.195 2.5-1.463 2.5-2.915V8.998c0-1.526-1.157-2.85-2.729-2.936A41.645 41.645 0 0014 6z" />
                        </svg>
                        Niveaux de Contrainte
                    </h2>
                </div>
                <div className="p-1">
                    {isLoadingBlocages ? (
                        <Skeleton height={80} width="100%" />
                    ) : (
                        <div className="mt-2 space-y-3">
                            {blocages.length > 0 ? (
                                blocages.map((blocage) => (
                                    <BlocageRow
                                        key={blocage.id}
                                        blocage={blocage}
                                        blocageToDeleteId={blocageToDeleteId}
                                        onDelete={initiateDeleteBlocage}
                                        onCancelDelete={cancelDeleteBlocage}
                                        onConfirmDelete={confirmDeleteBlocage}
                                        onToggleRegularisation={
                                            handleToggleRegularisation
                                        }
                                        isDeleteModalOpen={isDeleteModalOpen}
                                        setIsDeleteModalOpen={
                                            setIsDeleteModalOpen
                                        }
                                    />
                                ))
                            ) : (
                                <div className="flex flex-col items-center justify-center py-8 text-gray-400">
                                    <svg
                                        className="w-12 h-12 mb-2"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="M9.75 17L6 21m0 0l-3.75-4M6 21V3"
                                        />
                                    </svg>
                                    <span className="text-lg">
                                        Aucun point de blocage identifié pour ce
                                        cas.
                                    </span>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>

            {/* Modal for delete confirmation */}
            <LazyModal
                isOpen={isDeleteModalOpen}
                onClose={cancelDeleteBlocage} // Use the new function here
                title="Êtes-vous sûr de vouloir supprimer ce blocage ? Cette action est irréversible."
            >
                <div className="mt-4 flex justify-end space-x-3">
                    <LazyButton
                        variant="secondary"
                        onClick={cancelDeleteBlocage}
                    >
                        {" "}
                        {/* And here */}
                        Annuler
                    </LazyButton>
                    <LazyButton
                        variant="destructive"
                        onClick={confirmDeleteBlocage}
                    >
                        Supprimer
                    </LazyButton>
                </div>
            </LazyModal>

            {/* Modal for solution date */}
            {isSolutionModalOpen && currentBlocageForSolution && (
                <LazyModal
                    isOpen={isSolutionModalOpen}
                    onClose={() => {
                        setIsSolutionModalOpen(false);
                        setCurrentBlocageForSolution(null); // Important de réinitialiser
                        setSolutionDateError("");
                    }}
                    title={` ${currentBlocageForSolution.description}`}
                >
                    <form onSubmit={handleSolutionSubmit} className="space-y-4">
                        <div>
                            <label
                                htmlFor="solutionDate_modal"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Date de Résolution
                            </label>
                            <LazyInput
                                type="date"
                                name="solutionDate"
                                id="solutionDate_modal"
                                value={solutionDate}
                                onChange={(e) =>
                                    setSolutionDate(e.target.value)
                                }
                                required
                                className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                            />
                        </div>

                        <div>
                            <label
                                htmlFor="solutionResolution_modal"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Résolution
                            </label>
                            <ResolutionSelect
                                value={solutionResolution}
                                onChange={setSolutionResolution}
                                className="mt-1 block w-full"
                            />
                        </div>

                        <div>
                            <label
                                htmlFor="solutionDetailResolution_modal"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Détail de la résolution
                            </label>
                            <LazyTextArea
                                name="solutionDetailResolution"
                                id="solutionDetailResolution_modal"
                                value={solutionDetailResolution}
                                onChange={(e) =>
                                    setSolutionDetailResolution(e.target.value)
                                }
                                rows={3}
                                placeholder="Détails sur la résolution..."
                                className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                            />
                        </div>
                        {solutionDateError && (
                            <LazyFormError message={solutionDateError} />
                        )}
                        <div className="mt-6 flex justify-end space-x-3">
                            <LazyButton
                                type="button"
                                onClick={() => {
                                    setIsSolutionModalOpen(false);
                                    setCurrentBlocageForSolution(null);
                                    setSolutionDateError("");
                                }}
                                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                            >
                                Annuler
                            </LazyButton>
                            <LazyButton
                                type="submit"
                                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                            >
                                Valider
                            </LazyButton>
                        </div>
                    </form>
                </LazyModal>
            )}

            {/* Section KML Editor */}
            {casMain && (
                <section className="mt-8">
                    <CasKMLEditor
                        cas={casMain}
                        onKMLUpdated={handleKMLUpdated}
                        onKMLRemoved={handleKMLRemoved}
                        onError={handleKMLError}
                        readOnly={!canUploadFiles}
                    />
                    {kmlError && (
                        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                            <p className="text-red-800 text-sm">{kmlError}</p>
                        </div>
                    )}
                </section>
            )}

            {/* Section Carte - Affichée même sans données GeoJSON */}
            {/* <section className="mt-8 bg-white rounded-xl shadow p-4">
                <h3 className="font-bold mb-4">Géométrie du cas</h3>
                <CasMap
                    key={`cas-map-${casMain.id}`}
                    geojsonData={casMain.geojson}
                    casId={casMain.id || casId}
                    onMapReady={() =>
                        console.log("Carte prête pour le cas:", casMain.id)
                    }
                />
            </section> */}
        </div>
    );
}

// Add this CSS to your global styles or Tailwind config:
/*
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}
.animate-fade-in {
  animation: fade-in 0.5s ease-in;
}
*/
