/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cas/route";
exports.ids = ["app/api/cas/route"];
exports.modules = {

/***/ "(rsc)/./app/api/cas/route.ts":
/*!******************************!*\
  !*** ./app/api/cas/route.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/permissions */ \"(rsc)/./lib/permissions.ts\");\n/* harmony import */ var _lib_resolution_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/resolution-utils */ \"(rsc)/./lib/resolution-utils.ts\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_9__);\n\n\n\n\n\n // Assuming this is your auth library\n\n\n\n\n\nconst pipelineAsync = (0,util__WEBPACK_IMPORTED_MODULE_8__.promisify)(stream__WEBPACK_IMPORTED_MODULE_9__.pipeline);\n// Get all cas - filtered by user role\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const encrageId = searchParams.get(\"encrageId\");\n        const problematiqueId = searchParams.get(\"problematiqueId\"); // Lire le problematiqueId\n        const withGeojson = searchParams.get(\"withGeojson\") === \"true\";\n        const regularisation = searchParams.get(\"regularisation\");\n        const casStatus = searchParams.get(\"casStatus\");\n        const wilayaId = searchParams.get(\"wilayaId\");\n        // Pagination parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const pageSize = parseInt(searchParams.get(\"pageSize\") || \"20\");\n        const search = searchParams.get(\"search\") || \"\";\n        // Validate pagination parameters\n        if (page < 1) return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Page must be greater than 0\"\n        }, {\n            status: 400\n        });\n        if (pageSize < 1 || pageSize > 1000) return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Page size must be between 1 and 1000\"\n        }, {\n            status: 400\n        });\n        // Get the current user from the token\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)(); // Await the cookies() call\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token); // Assuming verifyToken is async\n        if (!userPayload) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        // Build the where clause based on filters and user role\n        let where = {};\n        // Si problematiqueId est fourni, il a la priorité pour le filtrage direct des Cas\n        if (problematiqueId) {\n            where.problematiqueId = problematiqueId;\n        } else if (encrageId) {\n            // Sinon, si encrageId est fourni, filtre les Cas via l'encrage de leur problématique\n            where.problematique = {\n                encrageId: encrageId\n            };\n        }\n        // Filtrage par statut de régularisation\n        if (regularisation === \"true\") {\n            where.regularisation = true;\n        } else if (regularisation === \"false\") {\n            where.regularisation = false;\n        }\n        // Si withGeojson est true, ne retourner que les cas avec des coordonnées\n        if (withGeojson) {\n            where.geojson = {\n                not: null\n            };\n        }\n        // Search functionality\n        if (search) {\n            where.OR = [\n                {\n                    nom: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    nif: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    nin: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    communes: {\n                        some: {\n                            nom: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        }\n                    }\n                }\n            ];\n        }\n        // Filtrage par wilayaId\n        if (userPayload.role === \"BASIC\" || userPayload.role === \"EDITOR\") {\n            // Pour BASIC et EDITOR, filtrer par leur wilayaId uniquement\n            if (userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                where.wilayaId = Number(userPayload.wilayaId);\n            }\n        } else if (userPayload.role === \"ADMIN\" || userPayload.role === \"VIEWER\") {\n            // Pour ADMIN et VIEWER, permettre le filtrage par wilayaId via paramètre\n            if (wilayaId && !isNaN(Number(wilayaId))) {\n                where.wilayaId = Number(wilayaId);\n            } else if (userPayload.role === \"VIEWER\" && userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                where.wilayaId = Number(userPayload.wilayaId);\n            }\n        }\n        // Pour le filtre par statut, nous devons utiliser une approche par batches\n        // car le statut dépend des résolutions de blocage\n        let needsStatusFiltering = casStatus && [\n            \"REGULARISE\",\n            \"AJOURNE\",\n            \"NON_EXAMINE\",\n            \"REJETE\"\n        ].includes(casStatus);\n        let totalCount;\n        let totalPages;\n        let skip;\n        let cas = [];\n        if (!needsStatusFiltering) {\n            // Cas normal - pas de filtre par statut\n            totalCount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                where\n            });\n            totalPages = Math.ceil(totalCount / pageSize);\n            skip = (page - 1) * pageSize;\n            cas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.findMany({\n                where,\n                include: {\n                    problematique: {\n                        include: {\n                            encrage: true\n                        }\n                    },\n                    user: {\n                        select: {\n                            id: true,\n                            username: true,\n                            role: true\n                        }\n                    },\n                    communes: true,\n                    blocage: {\n                        select: {\n                            resolution: true\n                        }\n                    }\n                },\n                skip,\n                take: pageSize,\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            });\n        } else {\n            // Cas avec filtre par statut - utiliser une approche optimisée par batches\n            const BATCH_SIZE = 200; // Taille réduite pour éviter trop de bind variables\n            let allFilteredCas = [];\n            let currentSkip = 0;\n            let hasMore = true;\n            let targetRecordsNeeded = page * pageSize; // Nombre total de records nécessaires pour cette page\n            // Phase 1: Récupérer par batches avec des includes minimes pour le filtrage initial\n            while(hasMore && allFilteredCas.length < targetRecordsNeeded){\n                const batch = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.findMany({\n                    where,\n                    include: {\n                        blocage: {\n                            select: {\n                                resolution: true\n                            }\n                        }\n                    },\n                    skip: currentSkip,\n                    take: BATCH_SIZE,\n                    orderBy: {\n                        createdAt: \"desc\"\n                    }\n                });\n                if (batch.length === 0) {\n                    hasMore = false;\n                    break;\n                }\n                // Filtrer le batch par statut\n                const filteredBatch = batch.filter((c)=>{\n                    const resolutions = c.blocage.map((b)=>b.resolution);\n                    const actualStatus = (0,_lib_resolution_utils__WEBPACK_IMPORTED_MODULE_7__.getCasStatus)(resolutions);\n                    return actualStatus === casStatus;\n                });\n                allFilteredCas.push(...filteredBatch);\n                currentSkip += BATCH_SIZE;\n                // Si on a récupéré moins que BATCH_SIZE, on a atteint la fin\n                if (batch.length < BATCH_SIZE) {\n                    hasMore = false;\n                }\n            }\n            // Phase 2: Récupérer les données complètes pour la page demandée seulement\n            const startIndex = (page - 1) * pageSize;\n            const casForPage = allFilteredCas.slice(startIndex, startIndex + pageSize);\n            if (casForPage.length > 0) {\n                // Récupérer les IDs des cas à afficher\n                const casIds = casForPage.map((c)=>c.id);\n                // Récupérer les données complètes par batches d'IDs pour éviter trop de bind variables\n                const ID_BATCH_SIZE = 100;\n                cas = [];\n                for(let i = 0; i < casIds.length; i += ID_BATCH_SIZE){\n                    const idBatch = casIds.slice(i, i + ID_BATCH_SIZE);\n                    const fullDataBatch = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.findMany({\n                        where: {\n                            id: {\n                                in: idBatch\n                            }\n                        },\n                        include: {\n                            problematique: {\n                                include: {\n                                    encrage: true\n                                }\n                            },\n                            user: {\n                                select: {\n                                    id: true,\n                                    username: true,\n                                    role: true\n                                }\n                            },\n                            communes: true,\n                            blocage: {\n                                select: {\n                                    resolution: true\n                                }\n                            }\n                        },\n                        orderBy: {\n                            createdAt: \"desc\"\n                        }\n                    });\n                    cas.push(...fullDataBatch);\n                }\n                // Maintenir l'ordre original\n                cas.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n            }\n            // Calculer la pagination\n            totalCount = allFilteredCas.length;\n            totalPages = Math.ceil(totalCount / pageSize);\n        // Note: Cette approche donne un count précis pour les données récupérées,\n        // mais ne compte pas nécessairement tous les enregistrements de la base.\n        // C'est un compromis pour éviter les problèmes de performances.\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            data: cas,\n            pagination: {\n                page,\n                pageSize,\n                totalCount,\n                totalPages,\n                hasNextPage: page < totalPages,\n                hasPrevPage: page > 1\n            }\n        });\n    } catch (error) {\n        console.error(\"ERREUR API_CAS_GET:\", error); // Log détaillé de l'erreur côté serveur\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientKnownRequestError) {\n            // Erreurs connues de Prisma (ex: contrainte violée, enregistrement non trouvé)\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: `Erreur de base de données (Prisma): ${error.code}`,\n                message: error.message,\n                details: error.meta\n            }, {\n                status: 500\n            });\n        } else if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientValidationError) {\n            // Erreurs de validation de Prisma (ex: type de champ incorrect)\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Erreur de validation des données (Prisma).\",\n                message: error.message\n            }, {\n                status: 400\n            });\n        }\n        // Pour les autres types d'erreurs, utilisez le gestionnaire générique ou un message par défaut\n        // return handleError(error); // Si handleError est suffisant\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Erreur interne du serveur lors de la récupération des cas.\",\n            message: error instanceof Error ? error.message : \"Une erreur inconnue est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Create new cas\n// Mettre à jour le schéma Zod\nconst casSchema = zod__WEBPACK_IMPORTED_MODULE_10__.z.object({\n    nom: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Le nom est requis.\"),\n    genre: zod__WEBPACK_IMPORTED_MODULE_10__.z.nativeEnum(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne, {\n        errorMap: (issue, ctx)=>{\n            if (issue.code === zod__WEBPACK_IMPORTED_MODULE_10__.z.ZodIssueCode.invalid_enum_value) {\n                return {\n                    message: \"La valeur du genre est invalide.\"\n                };\n            }\n            return {\n                message: ctx.defaultError\n            };\n        }\n    }),\n    nif: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional().nullable(),\n    nin: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional().nullable(),\n    superficie: zod__WEBPACK_IMPORTED_MODULE_10__.z.number().positive(\"La superficie doit être un nombre positif.\"),\n    // MODIFIÉ: regularisation est maintenant optionnel et défaut à false\n    regularisation: zod__WEBPACK_IMPORTED_MODULE_10__.z.boolean().optional().default(false),\n    observation: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional().nullable(),\n    problematiqueId: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().cuid(\"L'ID de la problématique est invalide.\"),\n    communeIds: zod__WEBPACK_IMPORTED_MODULE_10__.z.array(zod__WEBPACK_IMPORTED_MODULE_10__.z.string().regex(/^\\d+$/, \"Chaque ID de commune doit être une chaîne de chiffres.\")).min(1, \"Au moins une commune doit être sélectionnée.\"),\n    date_depot: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().datetime({\n        offset: true\n    }).optional().nullable()\n}).refine((data)=>{\n    if (data.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_MORALE) {\n        return !!data.nif; // NIF requis pour PERSONNE_MORALE\n    }\n    return true;\n}, {\n    message: \"Le NIF est requis et doit être valide pour une personne morale.\",\n    path: [\n        \"nif\"\n    ]\n}).refine((data)=>{\n    if (data.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_PHYSIQUE) {\n        return !!data.nin; // NIN requis pour PERSONNE_PHYSIQUE\n    }\n    return true;\n}, {\n    message: \"Le NIN est requis et doit être valide pour une personne physique.\",\n    path: [\n        \"nin\"\n    ]\n});\n// La transformation pour genre n'est plus nécessaire ici si le frontend envoie déjà les bonnes valeurs\n// et que z.nativeEnum(TypePersonne) est utilisé.\nasync function POST(request) {\n    try {\n        // Check write permissions using the new permission system\n        const { hasPermission, user, error } = await (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_6__.requireWritePermission)();\n        if (!hasPermission || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: error || \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const validation = casSchema.safeParse(body);\n        if (!validation.success) {\n            console.error(\"Validation errors:\", validation.error.flatten());\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(validation.error.flatten(), {\n                status: 400\n            });\n        }\n        // 'userId' est maintenant pris de userPayload.id\n        // 'regularisation' est maintenant inclus dans validation.data\n        const { nom, nif, nin, genre: genreString, date_depot, superficie, regularisation, observation, problematiqueId, communeIds } = validation.data;\n        // Utiliser user pour wilayaId - ADMIN peut créer des cas pour toutes les wilayas\n        // Note: wilayaId is required in the database, so we need to provide a valid value\n        if (!user.wilayaId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"WilayaId is required for all users\"\n            }, {\n                status: 400\n            });\n        }\n        const wilayaId = user.wilayaId;\n        const newCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.create({\n            data: {\n                nom,\n                nif: genreString === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_MORALE ? nif : null,\n                nin: genreString === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_PHYSIQUE ? nin : null,\n                genre: _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne[genreString],\n                date_depot: date_depot ? new Date(date_depot).toISOString() : null,\n                superficie,\n                regularisation,\n                observation,\n                problematiqueId,\n                userId: user.id,\n                wilayaId,\n                communes: {\n                    connect: communeIds.map((id)=>({\n                            id: parseInt(id)\n                        }))\n                }\n            },\n            include: {\n                problematique: true,\n                user: true,\n                communes: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(newCas, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"POST /api/cas error:\", error);\n        // Amélioration de la gestion des erreurs Prisma\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientKnownRequestError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                message: error.message,\n                code: error.code,\n                meta: error.meta\n            }, {\n                status: 400\n            });\n        }\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientValidationError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                message: error.message\n            }, {\n                status: 400\n            });\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/cas/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   forbidden: () => (/* binding */ forbidden),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   notFound: () => (/* binding */ notFound),\n/* harmony export */   unauthorized: () => (/* binding */ unauthorized),\n/* harmony export */   updateCasRegularisationStatus: () => (/* binding */ updateCasRegularisationStatus)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n // Importez Prisma pour typer les erreurs spécifiques\n\nasync function updateCasRegularisationStatus(casId) {\n    const relatedCasBlocages = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.blocage.findMany({\n        where: {\n            casId: casId\n        },\n        select: {\n            regularise: true\n        }\n    });\n    const allBlocagesRegularised = relatedCasBlocages.length > 0 && relatedCasBlocages.every((b)=>b.regularise);\n    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.cas.update({\n        where: {\n            id: casId\n        },\n        data: {\n            regularisation: allBlocagesRegularised\n        }\n    });\n}\nfunction handleError(error) {\n    console.error(error); // Bon pour le débogage côté serveur\n    if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_1__.Prisma.PrismaClientKnownRequestError) {\n        // Erreurs connues de Prisma (contraintes uniques, etc.)\n        // Vous pouvez ajouter des codes d'erreur spécifiques ici si nécessaire\n        // Par exemple, P2002 pour violation de contrainte unique\n        if (error.code === \"P2002\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Une ressource avec ces identifiants existe déjà.\",\n                details: error.meta\n            }, {\n                status: 409\n            }); // Conflict\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur de base de données.\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n    if (error instanceof Error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Une erreur interne est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Une erreur inconnue est survenue.\"\n    }, {\n        status: 500\n    });\n}\nfunction forbidden(message = \"Accès interdit.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 403\n    });\n}\nfunction notFound(message = \"Ressource non trouvée.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 404\n    });\n}\nfunction unauthorized() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Non autorisé\"\n    }, {\n        status: 401\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/permissions.ts":
/*!****************************!*\
  !*** ./lib/permissions.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentUserPermissions: () => (/* binding */ getCurrentUserPermissions),\n/* harmony export */   getPermissionsByRole: () => (/* binding */ getPermissionsByRole),\n/* harmony export */   requireDeletePermission: () => (/* binding */ requireDeletePermission),\n/* harmony export */   requireFileUploadPermission: () => (/* binding */ requireFileUploadPermission),\n/* harmony export */   requireMessagingPermission: () => (/* binding */ requireMessagingPermission),\n/* harmony export */   requireUserManagementPermission: () => (/* binding */ requireUserManagementPermission),\n/* harmony export */   requireWritePermission: () => (/* binding */ requireWritePermission)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n// Server-side permissions - only use in Server Components\n\n\n/**\n * Get user permissions based on their role\n */ function getPermissionsByRole(role) {\n    switch(role){\n        case \"ADMIN\":\n            return {\n                canRead: true,\n                canWrite: true,\n                canDelete: true,\n                canManageUsers: true,\n                canUploadFiles: true,\n                canSendMessages: true,\n                isReadOnly: false\n            };\n        case \"EDITOR\":\n            return {\n                canRead: true,\n                canWrite: true,\n                canDelete: true,\n                canManageUsers: false,\n                canUploadFiles: true,\n                canSendMessages: true,\n                isReadOnly: false\n            };\n        case \"BASIC\":\n            return {\n                canRead: true,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: true,\n                isReadOnly: true\n            };\n        case \"VIEWER\":\n            return {\n                canRead: true,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: false,\n                isReadOnly: true\n            };\n        default:\n            return {\n                canRead: false,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: false,\n                isReadOnly: true\n            };\n    }\n}\n/**\n * Get current user permissions from JWT token\n */ async function getCurrentUserPermissions() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return {\n                permissions: null,\n                user: null\n            };\n        }\n        const userPayload = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.verifyToken)(token);\n        if (!userPayload) {\n            return {\n                permissions: null,\n                user: null\n            };\n        }\n        const permissions = getPermissionsByRole(userPayload.role);\n        const user = {\n            id: userPayload.id,\n            role: userPayload.role,\n            username: userPayload.username,\n            email: userPayload.email,\n            wilayaId: userPayload.wilayaId\n        };\n        return {\n            permissions,\n            user\n        };\n    } catch (error) {\n        console.error(\"Error getting user permissions:\", error);\n        return {\n            permissions: null,\n            user: null\n        };\n    }\n}\n/**\n * Check if user has permission for write operations\n */ async function requireWritePermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canWrite) {\n        return {\n            hasPermission: false,\n            user,\n            error: user.role === \"VIEWER\" ? \"Read-only access: Write operations not permitted for VIEWER role\" : \"Insufficient permissions for write operations\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for delete operations\n */ async function requireDeletePermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canDelete) {\n        return {\n            hasPermission: false,\n            user,\n            error: user.role === \"VIEWER\" ? \"Read-only access: Delete operations not permitted for VIEWER role\" : \"Insufficient permissions for delete operations\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for user management\n */ async function requireUserManagementPermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canManageUsers) {\n        return {\n            hasPermission: false,\n            user,\n            error: \"Only ADMIN users can manage other users\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for file uploads\n */ async function requireFileUploadPermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canUploadFiles) {\n        return {\n            hasPermission: false,\n            user,\n            error: user.role === \"VIEWER\" ? \"Read-only access: File uploads not permitted for VIEWER role\" : \"Insufficient permissions for file uploads\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for sending messages\n */ async function requireMessagingPermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canSendMessages) {\n        return {\n            hasPermission: false,\n            user,\n            error: \"Read-only access: Messaging not permitted for VIEWER role\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/permissions.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./lib/resolution-utils.ts":
/*!*********************************!*\
  !*** ./lib/resolution-utils.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterCasByStatus: () => (/* binding */ filterCasByStatus),\n/* harmony export */   getCasStatisticsByResolution: () => (/* binding */ getCasStatisticsByResolution),\n/* harmony export */   getCasStatus: () => (/* binding */ getCasStatus),\n/* harmony export */   isCasAjourne: () => (/* binding */ isCasAjourne),\n/* harmony export */   isCasNonExamine: () => (/* binding */ isCasNonExamine),\n/* harmony export */   isCasRegularise: () => (/* binding */ isCasRegularise),\n/* harmony export */   updateCasRegularisationFromResolutions: () => (/* binding */ updateCasRegularisationFromResolutions)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n/**\n * Met à jour le statut de régularisation d'un cas basé sur les résolutions de ses blocages\n * Règles:\n * - Si tous les blocages sont ACCEPTE -> regularisation = true\n * - Si au moins un blocage est AJOURNE -> regularisation = false\n * - Si tous les blocages sont ATTENTE -> regularisation = false\n */ async function updateCasRegularisationFromResolutions(casId) {\n    try {\n        // Récupérer tous les blocages du cas avec leurs résolutions\n        const blocages = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.blocage.findMany({\n            where: {\n                casId\n            },\n            select: {\n                resolution: true\n            }\n        });\n        if (blocages.length === 0) {\n            // Aucun blocage, le cas reste non régularisé\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.update({\n                where: {\n                    id: casId\n                },\n                data: {\n                    regularisation: false\n                }\n            });\n            return;\n        }\n        const resolutions = blocages.map((b)=>b.resolution);\n        // Déterminer le nouveau statut de régularisation\n        let newRegularisationStatus = false;\n        if (resolutions.every((resolution)=>resolution === \"ACCEPTE\")) {\n            // Tous acceptés -> régularisé\n            newRegularisationStatus = true;\n        } else {\n            // Au moins un ajourné ou en attente -> non régularisé\n            newRegularisationStatus = false;\n        }\n        // Mettre à jour le cas\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.update({\n            where: {\n                id: casId\n            },\n            data: {\n                regularisation: newRegularisationStatus\n            }\n        });\n        console.log(`Cas ${casId} mis à jour: regularisation = ${newRegularisationStatus}`);\n    } catch (error) {\n        console.error(`Erreur lors de la mise à jour du cas ${casId}:`, error);\n        throw error;\n    }\n}\n/**\n * Calcule les statistiques des cas basées sur les résolutions des blocages\n * Filtre par wilaya selon le rôle de l'utilisateur\n */ async function getCasStatisticsByResolution(userPayload) {\n    try {\n        // Construire les conditions de filtrage\n        const where = {};\n        // Filtrage par wilayaId selon le rôle\n        if (userPayload) {\n            if (userPayload.role === \"BASIC\" || userPayload.role === \"EDITOR\") {\n                // Pour BASIC et EDITOR, filtrer par leur wilayaId uniquement\n                if (userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                    where.wilayaId = Number(userPayload.wilayaId);\n                }\n            } else if (userPayload.role === \"ADMIN\" || userPayload.role === \"VIEWER\") {\n            // Pour ADMIN et VIEWER, pas de filtrage par défaut (accès à toutes les wilayas)\n            // Le filtrage peut être ajouté via des paramètres si nécessaire\n            }\n        }\n        console.log(\"📊 getCasStatisticsByResolution - Utilisation de requête SQL optimisée...\");\n        console.time(\"resolution-stats-optimized\");\n        // Version optimisée avec requête SQL brute pour éviter les limites\n        let whereClauseSQL = \"\";\n        let params = [];\n        if (where.wilayaId) {\n            whereClauseSQL = 'WHERE c.\"wilayaId\" = $1';\n            params = [\n                where.wilayaId\n            ];\n        }\n        let casAjournes = 0;\n        let casNonExamines = 0;\n        let casRegularises = 0;\n        let casNonRegularises = 0;\n        let casRejetes = 0;\n        let totalCas = 0;\n        try {\n            // Utiliser une approche plus simple et fiable basée sur la logique getCasStatus\n            const cas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.findMany({\n                where: where,\n                include: {\n                    blocage: {\n                        select: {\n                            resolution: true\n                        }\n                    }\n                }\n            });\n            totalCas = cas.length;\n            // Calculer les statistiques en utilisant la logique officielle\n            cas.forEach((c)=>{\n                const resolutions = c.blocage.map((b)=>b.resolution);\n                const status = getCasStatus(resolutions);\n                switch(status){\n                    case \"REGULARISE\":\n                        casRegularises++;\n                        break;\n                    case \"AJOURNE\":\n                        casAjournes++;\n                        break;\n                    case \"REJETE\":\n                        casRejetes++;\n                        break;\n                    case \"NON_EXAMINE\":\n                        casNonExamines++;\n                        break;\n                }\n            });\n            // Les cas non régularisés sont tous sauf les régularisés\n            casNonRegularises = totalCas - casRegularises;\n            console.log(`📊 ${totalCas} cas traités avec requête optimisée`);\n        } catch (sqlError) {\n            console.error(\"Erreur SQL, fallback vers requête simple:\", sqlError);\n            // Fallback vers requête simple avec limite\n            totalCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                where\n            });\n            // Estimations simples\n            casRegularises = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                where: {\n                    ...where,\n                    regularisation: true\n                }\n            });\n            casNonExamines = totalCas - casRegularises;\n        }\n        console.timeEnd(\"resolution-stats-optimized\");\n        return {\n            total: totalCas,\n            regularises: casRegularises,\n            nonRegularises: casNonRegularises,\n            ajournes: casAjournes,\n            nonExamines: casNonExamines,\n            rejetes: casRejetes\n        };\n    } catch (error) {\n        console.error(\"Erreur lors du calcul des statistiques:\", error);\n        throw error;\n    }\n}\n/**\n * Détermine si un cas est ajourné (au moins un blocage ajourné)\n */ function isCasAjourne(resolutions) {\n    return resolutions.some((resolution)=>resolution === \"AJOURNE\");\n}\n/**\n * Détermine si un cas est non examiné (tous les blocages en attente ou aucun blocage)\n */ function isCasNonExamine(resolutions) {\n    return resolutions.length === 0 || resolutions.every((resolution)=>resolution === \"ATTENTE\");\n}\n/**\n * Détermine si un cas est régularisé (tous les blocages acceptés)\n */ function isCasRegularise(resolutions) {\n    return resolutions.length > 0 && resolutions.every((resolution)=>resolution === \"ACCEPTE\");\n}\n/**\n * Détermine le statut d'un cas basé sur ses résolutions\n * IMPORTANT: Cette fonction définit la logique de priorité officielle\n * Utilisez cette fonction dans tous les filtres pour maintenir la cohérence\n */ function getCasStatus(resolutions) {\n    if (resolutions.length === 0) {\n        return \"NON_EXAMINE\"; // Cas sans blocage\n    } else if (resolutions.every((r)=>r === \"ATTENTE\")) {\n        return \"NON_EXAMINE\"; // Tous en attente\n    } else if (resolutions.some((r)=>r === \"REJETE\")) {\n        return \"REJETE\"; // Au moins un rejeté (priorité la plus haute)\n    } else if (resolutions.some((r)=>r === \"AJOURNE\")) {\n        return \"AJOURNE\"; // Au moins un ajourné\n    } else if (resolutions.every((r)=>r === \"ACCEPTE\")) {\n        return \"REGULARISE\"; // Tous acceptés\n    } else {\n        return \"NON_EXAMINE\"; // Cas par défaut\n    }\n}\n/**\n * Filtre les cas par statut en utilisant la logique de priorité officielle\n * Utilisez cette fonction dans toutes les APIs pour maintenir la cohérence\n */ function filterCasByStatus(cas, targetStatus) {\n    if (![\n        \"REGULARISE\",\n        \"AJOURNE\",\n        \"NON_EXAMINE\",\n        \"REJETE\"\n    ].includes(targetStatus)) {\n        return cas; // Pas de filtrage si statut invalide\n    }\n    return cas.filter((c)=>{\n        const resolutions = c.blocage.map((b)=>b.resolution);\n        const actualStatus = getCasStatus(resolutions);\n        return actualStatus === targetStatus;\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/resolution-utils.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/cas/route.ts */ \"(rsc)/./app/api/cas/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cas/route\",\n        pathname: \"/api/cas\",\n        filename: \"route\",\n        bundlePath: \"app/api/cas/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\cas\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();