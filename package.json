{"name": "assainissement", "version": "0.1.0", "private": true, "scripts": {"clean": "rimraf .next .next-dev", "build": "next build", "dev": "next dev", "start": "next start", "lint": "next lint"}, "@headlessui/react": "^1.7.19", "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.10.0", "@tmcw/togeojson": "^7.1.2", "@types/pdfkit": "^0.17.3", "bcryptjs": "^2.4.3", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "csv-parser": "^3.2.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "leaflet": "^1.9.4", "next": "^15.3.2", "next-auth": "^4.24.11", "pdfkit": "^0.17.2", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-leaflet": "^4.2.1", "react-loading-skeleton": "^3.5.0", "tailwind-merge": "^3.2.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/leaflet": "^1.9.18", "@types/node": "^20.11.20", "@types/react": "^18.2.58", "@types/react-dom": "^18.2.19", "@types/xlsx": "^0.0.35", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "postcss-styled-syntax": "^0.7.1", "prisma": "^6.10.0", "rimraf": "^6.0.1", "stylelint": "^16.19.1", "stylelint-config-standard": "^38.0.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "prisma": {"seed": "ts-node --project prisma/tsconfig.seed.json prisma/seed.ts"}}