{"/api/auth/me/route": "app/api/auth/me/route.js", "/api/users/route": "app/api/users/route.js", "/api/communes/route": "app/api/communes/route.js", "/api/problematiques/route": "app/api/problematiques/route.js", "/api/encrages/route": "app/api/encrages/route.js", "/api/cas/route": "app/api/cas/route.js", "/api/cas/[id]/route": "app/api/cas/[id]/route.js", "/api/cas/[id]/blocages/route": "app/api/cas/[id]/blocages/route.js", "/api/secteurs/route": "app/api/secteurs/route.js", "/api/cas/[id]/pdf/route": "app/api/cas/[id]/pdf/route.js", "/users/page": "app/users/page.js", "/page": "app/page.js", "/dashboard/cas/page": "app/dashboard/cas/page.js", "/dashboard/cas/[id]/page": "app/dashboard/cas/[id]/page.js"}