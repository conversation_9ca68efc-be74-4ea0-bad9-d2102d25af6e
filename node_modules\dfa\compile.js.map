{"version": 3, "file": "compile.js", "sources": ["src/utils.js", "src/nodes.js", "src/grammar.js", "src/SymbolTable.js", "src/dfa.js", "src/StateMachine.js", "src/compile.js"], "sourcesContent": ["/**\n * Returns a new set representing the union of a and b.\n */\nexport function union(a, b) {\n  let s = new Set(a);\n  addAll(s, b);\n  return s;\n}\n\n/**\n * Adds all items from the set b to a.\n */\nexport function addAll(a, b) {\n  for (let x of b) {\n    a.add(x);\n  }\n}\n\n/**\n * Returns whether two sets are equal\n */\nexport function equal(a, b) {\n  if (a === b)\n    return true;\n\n  if (a.size !== b.size)\n    return false;\n\n  for (let x of a) {\n    if (!b.has(x)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import {addAll, union} from './utils';\n\n/**\n * Base AST node\n */\nexport class Node {\n  constructor() {\n    Object.defineProperty(this, 'followpos', {value: new Set})\n  }\n\n  calcFollowpos() {\n    for (let key in this) {\n      if (this[key] instanceof Node) {\n        this[key].calcFollowpos();\n      }\n    }\n  }\n}\n\n/**\n * Represents a variable reference\n */\nexport class Variable extends Node {\n  constructor(name) {\n    super();\n    this.name = name;\n  }\n\n  copy() {\n    return new Variable(this.name);\n  }\n}\n\n/**\n * Represents a comment\n */\nexport class Comment extends Node {\n  constructor(value) {\n    super();\n    this.value = value;\n  }\n}\n\n/**\n * Represents an assignment statement.\n * e.g. `variable = expression;`\n */\nexport class Assignment extends Node {\n  constructor(variable, expression) {\n    super();\n    this.variable = variable;\n    this.expression = expression;\n  }\n}\n\n/**\n * Represents an alternation.\n * e.g. `a | b`\n */\nexport class Alternation extends Node {\n  constructor(a, b) {\n    super();\n    this.a = a;\n    this.b = b;\n  }\n\n  get nullable() {\n    return this.a.nullable || this.b.nullable;\n  }\n\n  get firstpos() {\n    return union(this.a.firstpos, this.b.firstpos);\n  }\n\n  get lastpos() {\n    return union(this.a.lastpos, this.b.lastpos);\n  }\n\n  copy() {\n    return new Alternation(this.a.copy(), this.b.copy());\n  }\n}\n\n/**\n * Represents a concatenation, or chain.\n * e.g. `a b c`\n */\nexport class Concatenation extends Node {\n  constructor(a, b) {\n    super();\n    this.a = a;\n    this.b = b;\n  }\n\n  get nullable() {\n    return this.a.nullable && this.b.nullable;\n  }\n\n  get firstpos() {\n    let s = this.a.firstpos;\n    if (this.a.nullable) {\n      s = union(s, this.b.firstpos);\n    }\n\n    return s;\n  }\n\n  get lastpos() {\n    let s = this.b.lastpos;\n    if (this.b.nullable) {\n      s = union(s, this.a.lastpos);\n    }\n\n    return s;\n  }\n\n  calcFollowpos() {\n    super.calcFollowpos();\n    for (let n of this.a.lastpos) {\n      addAll(n.followpos, this.b.firstpos);\n    }\n  }\n\n  copy() {\n    return new Concatenation(this.a.copy(), this.b.copy());\n  }\n}\n\n/**\n * Represents a repetition.\n * e.g. `a+`, `b*`, or `c?`\n */\nexport class Repeat extends Node {\n  constructor(expression, op) {\n    super();\n    this.expression = expression;\n    this.op = op;\n  }\n\n  get nullable() {\n    return this.op === '*' || this.op === '?';\n  }\n\n  get firstpos() {\n    return this.expression.firstpos;\n  }\n\n  get lastpos() {\n    return this.expression.lastpos;\n  }\n\n  calcFollowpos() {\n    super.calcFollowpos();\n    if (this.op === '*' || this.op === '+') {\n      for (let n of this.lastpos) {\n        addAll(n.followpos, this.firstpos);\n      }\n    }\n  }\n\n  copy() {\n    return new Repeat(this.expression.copy(), this.op);\n  }\n}\n\nexport function buildRepetition(expression, min = 0, max = Infinity) {\n  if (min < 0 || min > max) {\n    throw new Error(`Invalid repetition range: ${min} ${max}`);\n  }\n\n  let res = null;\n  for (let i = 0; i < min; i++) {\n    res = concat(res, expression.copy());\n  }\n\n  if (max === Infinity) {\n    res = concat(res, new Repeat(expression.copy(), '*'));\n  } else {\n    for (let i = min; i < max; i++) {\n      res = concat(res, new Repeat(expression.copy(), '?'))\n    }\n  }\n\n  return res;\n}\n\nfunction concat(a, b) {\n  if (!a) {\n    return b;\n  }\n\n  return new Concatenation(a, b);\n}\n\n/**\n * Base class for leaf nodes\n */\nclass Leaf extends Node {\n  get nullable() {\n    return false;\n  }\n\n  get firstpos() {\n    return new Set([this]);\n  }\n\n  get lastpos() {\n    return new Set([this]);\n  }\n}\n\n/**\n * Represents a literal value, e.g. a number\n */\nexport class Literal extends Leaf {\n  constructor(value) {\n    super();\n    this.value = value;\n  }\n\n  copy() {\n    return new Literal(this.value);\n  }\n}\n\n/**\n * Marks the end of an expression\n */\nexport class EndMarker extends Leaf {}\n\n/**\n * Represents a tag\n * e.g. `a:(a b)`\n */\nexport class Tag extends Leaf {\n  constructor(name) {\n    super();\n    this.name = name;\n  }\n\n  get nullable() {\n    return true;\n  }\n\n  copy() {\n    return new Tag(this.name);\n  }\n}\n", "/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n\n\"use strict\";\n\nfunction peg$subclass(child, parent) {\n  function ctor() { this.constructor = child; }\n  ctor.prototype = parent.prototype;\n  child.prototype = new ctor();\n}\n\nfunction peg$SyntaxError(message, expected, found, location) {\n  this.message  = message;\n  this.expected = expected;\n  this.found    = found;\n  this.location = location;\n  this.name     = \"SyntaxError\";\n\n  if (typeof Error.captureStackTrace === \"function\") {\n    Error.captureStackTrace(this, peg$SyntaxError);\n  }\n}\n\npeg$subclass(peg$SyntaxError, Error);\n\npeg$SyntaxError.buildMessage = function(expected, found) {\n  var DESCRIBE_EXPECTATION_FNS = {\n        literal: function(expectation) {\n          return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n        },\n\n        \"class\": function(expectation) {\n          var escapedParts = \"\",\n              i;\n\n          for (i = 0; i < expectation.parts.length; i++) {\n            escapedParts += expectation.parts[i] instanceof Array\n              ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1])\n              : classEscape(expectation.parts[i]);\n          }\n\n          return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n        },\n\n        any: function(expectation) {\n          return \"any character\";\n        },\n\n        end: function(expectation) {\n          return \"end of input\";\n        },\n\n        other: function(expectation) {\n          return expectation.description;\n        }\n      };\n\n  function hex(ch) {\n    return ch.charCodeAt(0).toString(16).toUpperCase();\n  }\n\n  function literalEscape(s) {\n    return s\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\"/g,  '\\\\\"')\n      .replace(/\\0/g, '\\\\0')\n      .replace(/\\t/g, '\\\\t')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n  }\n\n  function classEscape(s) {\n    return s\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\\]/g, '\\\\]')\n      .replace(/\\^/g, '\\\\^')\n      .replace(/-/g,  '\\\\-')\n      .replace(/\\0/g, '\\\\0')\n      .replace(/\\t/g, '\\\\t')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n  }\n\n  function describeExpectation(expectation) {\n    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n  }\n\n  function describeExpected(expected) {\n    var descriptions = new Array(expected.length),\n        i, j;\n\n    for (i = 0; i < expected.length; i++) {\n      descriptions[i] = describeExpectation(expected[i]);\n    }\n\n    descriptions.sort();\n\n    if (descriptions.length > 0) {\n      for (i = 1, j = 1; i < descriptions.length; i++) {\n        if (descriptions[i - 1] !== descriptions[i]) {\n          descriptions[j] = descriptions[i];\n          j++;\n        }\n      }\n      descriptions.length = j;\n    }\n\n    switch (descriptions.length) {\n      case 1:\n        return descriptions[0];\n\n      case 2:\n        return descriptions[0] + \" or \" + descriptions[1];\n\n      default:\n        return descriptions.slice(0, -1).join(\", \")\n          + \", or \"\n          + descriptions[descriptions.length - 1];\n    }\n  }\n\n  function describeFound(found) {\n    return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n  }\n\n  return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n};\n\nfunction peg$parse(input, options) {\n  options = options !== void 0 ? options : {};\n\n  var peg$FAILED = {},\n\n      peg$startRuleFunctions = { rules: peg$parserules },\n      peg$startRuleFunction  = peg$parserules,\n\n      peg$c0 = function(s) { return s },\n      peg$c1 = \"#\",\n      peg$c2 = peg$literalExpectation(\"#\", false),\n      peg$c3 = /^[^\\r\\n]/,\n      peg$c4 = peg$classExpectation([\"\\r\", \"\\n\"], true, false),\n      peg$c5 = /^[\\r\\n]/,\n      peg$c6 = peg$classExpectation([\"\\r\", \"\\n\"], false, false),\n      peg$c7 = function(v) { return new n.Comment(v.join('')) },\n      peg$c8 = \"=\",\n      peg$c9 = peg$literalExpectation(\"=\", false),\n      peg$c10 = \";\",\n      peg$c11 = peg$literalExpectation(\";\", false),\n      peg$c12 = function(v, e) { return new n.Assignment(v, e) },\n      peg$c13 = function(v) { return new n.Variable(v) },\n      peg$c14 = \"|\",\n      peg$c15 = peg$literalExpectation(\"|\", false),\n      peg$c16 = function(a, b) { return new n.Alternation(a, b) },\n      peg$c17 = function(a, b) { return new n.Concatenation(a, b) },\n      peg$c18 = \":\",\n      peg$c19 = peg$literalExpectation(\":\", false),\n      peg$c20 = function(t, e) { return new n.Concatenation(e, new n.Tag(t)) },\n      peg$c21 = \"*\",\n      peg$c22 = peg$literalExpectation(\"*\", false),\n      peg$c23 = function(t) { return new n.Repeat(t, '*') },\n      peg$c24 = \"?\",\n      peg$c25 = peg$literalExpectation(\"?\", false),\n      peg$c26 = function(t) { return new n.Repeat(t, '?') },\n      peg$c27 = \"+\",\n      peg$c28 = peg$literalExpectation(\"+\", false),\n      peg$c29 = function(t) { return new n.Repeat(t, '+') },\n      peg$c30 = \"{\",\n      peg$c31 = peg$literalExpectation(\"{\", false),\n      peg$c32 = \"}\",\n      peg$c33 = peg$literalExpectation(\"}\", false),\n      peg$c34 = function(t, m) { return n.buildRepetition(t, m, m) },\n      peg$c35 = \",\",\n      peg$c36 = peg$literalExpectation(\",\", false),\n      peg$c37 = function(t, min) { return n.buildRepetition(t, min, Infinity) },\n      peg$c38 = function(t, max) { return n.buildRepetition(t, 0, max) },\n      peg$c39 = function(t, min, max) { return n.buildRepetition(t, min, max) },\n      peg$c40 = function(x) { return new n.Literal(x) },\n      peg$c41 = \"(\",\n      peg$c42 = peg$literalExpectation(\"(\", false),\n      peg$c43 = \")\",\n      peg$c44 = peg$literalExpectation(\")\", false),\n      peg$c45 = function(e) { return e },\n      peg$c46 = function() { return n.buildRepetition() },\n      peg$c47 = function(a, b) { return a + b.join('') },\n      peg$c48 = \"_\",\n      peg$c49 = peg$literalExpectation(\"_\", false),\n      peg$c50 = /^[a-zA-Z]/,\n      peg$c51 = peg$classExpectation([[\"a\", \"z\"], [\"A\", \"Z\"]], false, false),\n      peg$c52 = /^[0-9]/,\n      peg$c53 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n      peg$c54 = function(num) { return parseInt(num.join('')) },\n      peg$c55 = /^[ \\t\\r\\n]/,\n      peg$c56 = peg$classExpectation([\" \", \"\\t\", \"\\r\", \"\\n\"], false, false),\n\n      peg$currPos          = 0,\n      peg$savedPos         = 0,\n      peg$posDetailsCache  = [{ line: 1, column: 1 }],\n      peg$maxFailPos       = 0,\n      peg$maxFailExpected  = [],\n      peg$silentFails      = 0,\n\n      peg$result;\n\n  if (\"startRule\" in options) {\n    if (!(options.startRule in peg$startRuleFunctions)) {\n      throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n    }\n\n    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n  }\n\n  function text() {\n    return input.substring(peg$savedPos, peg$currPos);\n  }\n\n  function location() {\n    return peg$computeLocation(peg$savedPos, peg$currPos);\n  }\n\n  function expected(description, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n    throw peg$buildStructuredError(\n      [peg$otherExpectation(description)],\n      input.substring(peg$savedPos, peg$currPos),\n      location\n    );\n  }\n\n  function error(message, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n    throw peg$buildSimpleError(message, location);\n  }\n\n  function peg$literalExpectation(text, ignoreCase) {\n    return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n  }\n\n  function peg$classExpectation(parts, inverted, ignoreCase) {\n    return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n  }\n\n  function peg$anyExpectation() {\n    return { type: \"any\" };\n  }\n\n  function peg$endExpectation() {\n    return { type: \"end\" };\n  }\n\n  function peg$otherExpectation(description) {\n    return { type: \"other\", description: description };\n  }\n\n  function peg$computePosDetails(pos) {\n    var details = peg$posDetailsCache[pos], p;\n\n    if (details) {\n      return details;\n    } else {\n      p = pos - 1;\n      while (!peg$posDetailsCache[p]) {\n        p--;\n      }\n\n      details = peg$posDetailsCache[p];\n      details = {\n        line:   details.line,\n        column: details.column\n      };\n\n      while (p < pos) {\n        if (input.charCodeAt(p) === 10) {\n          details.line++;\n          details.column = 1;\n        } else {\n          details.column++;\n        }\n\n        p++;\n      }\n\n      peg$posDetailsCache[pos] = details;\n      return details;\n    }\n  }\n\n  function peg$computeLocation(startPos, endPos) {\n    var startPosDetails = peg$computePosDetails(startPos),\n        endPosDetails   = peg$computePosDetails(endPos);\n\n    return {\n      start: {\n        offset: startPos,\n        line:   startPosDetails.line,\n        column: startPosDetails.column\n      },\n      end: {\n        offset: endPos,\n        line:   endPosDetails.line,\n        column: endPosDetails.column\n      }\n    };\n  }\n\n  function peg$fail(expected) {\n    if (peg$currPos < peg$maxFailPos) { return; }\n\n    if (peg$currPos > peg$maxFailPos) {\n      peg$maxFailPos = peg$currPos;\n      peg$maxFailExpected = [];\n    }\n\n    peg$maxFailExpected.push(expected);\n  }\n\n  function peg$buildSimpleError(message, location) {\n    return new peg$SyntaxError(message, null, null, location);\n  }\n\n  function peg$buildStructuredError(expected, found, location) {\n    return new peg$SyntaxError(\n      peg$SyntaxError.buildMessage(expected, found),\n      expected,\n      found,\n      location\n    );\n  }\n\n  function peg$parserules() {\n    var s0, s1;\n\n    s0 = [];\n    s1 = peg$parsestatement();\n    if (s1 !== peg$FAILED) {\n      while (s1 !== peg$FAILED) {\n        s0.push(s1);\n        s1 = peg$parsestatement();\n      }\n    } else {\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsestatement() {\n    var s0, s1, s2;\n\n    s0 = peg$currPos;\n    s1 = peg$parsestatement_type();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parse_();\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c0(s1);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsestatement_type() {\n    var s0;\n\n    s0 = peg$parseassignment();\n    if (s0 === peg$FAILED) {\n      s0 = peg$parsecomment();\n    }\n\n    return s0;\n  }\n\n  function peg$parsecomment() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (input.charCodeAt(peg$currPos) === 35) {\n      s1 = peg$c1;\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c2); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      if (peg$c3.test(input.charAt(peg$currPos))) {\n        s3 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c4); }\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        if (peg$c3.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c4); }\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        if (peg$c5.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c6); }\n        }\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c7(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parseassignment() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n\n    s0 = peg$currPos;\n    s1 = peg$parsevariable();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parse_();\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s3 = peg$c8;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c9); }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parse_();\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsealternation();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parse_();\n              if (s6 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 59) {\n                  s7 = peg$c10;\n                  peg$currPos++;\n                } else {\n                  s7 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c11); }\n                }\n                if (s7 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c12(s1, s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsevariable() {\n    var s0, s1;\n\n    s0 = peg$currPos;\n    s1 = peg$parsename();\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c13(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parsealternation() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parseconcatenation();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parse_();\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 124) {\n          s3 = peg$c14;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c15); }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parse_();\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsealternation();\n            if (s5 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c16(s1, s5);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$parseconcatenation();\n    }\n\n    return s0;\n  }\n\n  function peg$parseconcatenation() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$parserepeat();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parse_();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseconcatenation();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c17(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$parserepeat();\n    }\n\n    return s0;\n  }\n\n  function peg$parserepeat() {\n    var s0, s1, s2, s3, s4, s5, s6;\n\n    s0 = peg$currPos;\n    s1 = peg$parsename();\n    if (s1 !== peg$FAILED) {\n      if (input.charCodeAt(peg$currPos) === 58) {\n        s2 = peg$c18;\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c19); }\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parserepeat();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c20(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$parseterm();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 42) {\n          s2 = peg$c21;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c22); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c23(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parseterm();\n        if (s1 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 63) {\n            s2 = peg$c24;\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c25); }\n          }\n          if (s2 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c26(s1);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parseterm();\n          if (s1 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 43) {\n              s2 = peg$c27;\n              peg$currPos++;\n            } else {\n              s2 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c28); }\n            }\n            if (s2 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c29(s1);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n          if (s0 === peg$FAILED) {\n            s0 = peg$currPos;\n            s1 = peg$parseterm();\n            if (s1 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 123) {\n                s2 = peg$c30;\n                peg$currPos++;\n              } else {\n                s2 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c31); }\n              }\n              if (s2 !== peg$FAILED) {\n                s3 = peg$parsenumber();\n                if (s3 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 125) {\n                    s4 = peg$c32;\n                    peg$currPos++;\n                  } else {\n                    s4 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c33); }\n                  }\n                  if (s4 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c34(s1, s3);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n            if (s0 === peg$FAILED) {\n              s0 = peg$currPos;\n              s1 = peg$parseterm();\n              if (s1 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 123) {\n                  s2 = peg$c30;\n                  peg$currPos++;\n                } else {\n                  s2 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c31); }\n                }\n                if (s2 !== peg$FAILED) {\n                  s3 = peg$parsenumber();\n                  if (s3 !== peg$FAILED) {\n                    if (input.charCodeAt(peg$currPos) === 44) {\n                      s4 = peg$c35;\n                      peg$currPos++;\n                    } else {\n                      s4 = peg$FAILED;\n                      if (peg$silentFails === 0) { peg$fail(peg$c36); }\n                    }\n                    if (s4 !== peg$FAILED) {\n                      if (input.charCodeAt(peg$currPos) === 125) {\n                        s5 = peg$c32;\n                        peg$currPos++;\n                      } else {\n                        s5 = peg$FAILED;\n                        if (peg$silentFails === 0) { peg$fail(peg$c33); }\n                      }\n                      if (s5 !== peg$FAILED) {\n                        peg$savedPos = s0;\n                        s1 = peg$c37(s1, s3);\n                        s0 = s1;\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n              if (s0 === peg$FAILED) {\n                s0 = peg$currPos;\n                s1 = peg$parseterm();\n                if (s1 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 123) {\n                    s2 = peg$c30;\n                    peg$currPos++;\n                  } else {\n                    s2 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c31); }\n                  }\n                  if (s2 !== peg$FAILED) {\n                    if (input.charCodeAt(peg$currPos) === 44) {\n                      s3 = peg$c35;\n                      peg$currPos++;\n                    } else {\n                      s3 = peg$FAILED;\n                      if (peg$silentFails === 0) { peg$fail(peg$c36); }\n                    }\n                    if (s3 !== peg$FAILED) {\n                      s4 = peg$parsenumber();\n                      if (s4 !== peg$FAILED) {\n                        if (input.charCodeAt(peg$currPos) === 125) {\n                          s5 = peg$c32;\n                          peg$currPos++;\n                        } else {\n                          s5 = peg$FAILED;\n                          if (peg$silentFails === 0) { peg$fail(peg$c33); }\n                        }\n                        if (s5 !== peg$FAILED) {\n                          peg$savedPos = s0;\n                          s1 = peg$c38(s1, s4);\n                          s0 = s1;\n                        } else {\n                          peg$currPos = s0;\n                          s0 = peg$FAILED;\n                        }\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n                if (s0 === peg$FAILED) {\n                  s0 = peg$currPos;\n                  s1 = peg$parseterm();\n                  if (s1 !== peg$FAILED) {\n                    if (input.charCodeAt(peg$currPos) === 123) {\n                      s2 = peg$c30;\n                      peg$currPos++;\n                    } else {\n                      s2 = peg$FAILED;\n                      if (peg$silentFails === 0) { peg$fail(peg$c31); }\n                    }\n                    if (s2 !== peg$FAILED) {\n                      s3 = peg$parsenumber();\n                      if (s3 !== peg$FAILED) {\n                        if (input.charCodeAt(peg$currPos) === 44) {\n                          s4 = peg$c35;\n                          peg$currPos++;\n                        } else {\n                          s4 = peg$FAILED;\n                          if (peg$silentFails === 0) { peg$fail(peg$c36); }\n                        }\n                        if (s4 !== peg$FAILED) {\n                          s5 = peg$parsenumber();\n                          if (s5 !== peg$FAILED) {\n                            if (input.charCodeAt(peg$currPos) === 125) {\n                              s6 = peg$c32;\n                              peg$currPos++;\n                            } else {\n                              s6 = peg$FAILED;\n                              if (peg$silentFails === 0) { peg$fail(peg$c33); }\n                            }\n                            if (s6 !== peg$FAILED) {\n                              peg$savedPos = s0;\n                              s1 = peg$c39(s1, s3, s5);\n                              s0 = s1;\n                            } else {\n                              peg$currPos = s0;\n                              s0 = peg$FAILED;\n                            }\n                          } else {\n                            peg$currPos = s0;\n                            s0 = peg$FAILED;\n                          }\n                        } else {\n                          peg$currPos = s0;\n                          s0 = peg$FAILED;\n                        }\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                  if (s0 === peg$FAILED) {\n                    s0 = peg$parseterm();\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return s0;\n  }\n\n  function peg$parseterm() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$parsevariable();\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$parsenumber();\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c40(s1);\n      }\n      s0 = s1;\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s1 = peg$c41;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c42); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parsealternation();\n          if (s2 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 41) {\n              s3 = peg$c43;\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c44); }\n            }\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c45(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      }\n    }\n\n    return s0;\n  }\n\n  function peg$parserepetition() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (input.charCodeAt(peg$currPos) === 123) {\n      s1 = peg$c30;\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c31); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsenumber();\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 125) {\n          s3 = peg$c32;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c33); }\n        }\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c46();\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsename() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$parsename_start_char();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsename_char();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsename_char();\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c47(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsename_start_char() {\n    var s0;\n\n    if (input.charCodeAt(peg$currPos) === 95) {\n      s0 = peg$c48;\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c49); }\n    }\n    if (s0 === peg$FAILED) {\n      if (peg$c50.test(input.charAt(peg$currPos))) {\n        s0 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c51); }\n      }\n    }\n\n    return s0;\n  }\n\n  function peg$parsename_char() {\n    var s0;\n\n    s0 = peg$parsename_start_char();\n    if (s0 === peg$FAILED) {\n      if (peg$c52.test(input.charAt(peg$currPos))) {\n        s0 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c53); }\n      }\n    }\n\n    return s0;\n  }\n\n  function peg$parsenumber() {\n    var s0, s1, s2;\n\n    s0 = peg$currPos;\n    s1 = [];\n    if (peg$c52.test(input.charAt(peg$currPos))) {\n      s2 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c53); }\n    }\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        if (peg$c52.test(input.charAt(peg$currPos))) {\n          s2 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c53); }\n        }\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c54(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parse_() {\n    var s0, s1;\n\n    s0 = [];\n    if (peg$c55.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c56); }\n    }\n    while (s1 !== peg$FAILED) {\n      s0.push(s1);\n      if (peg$c55.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c56); }\n      }\n    }\n\n    return s0;\n  }\n\n\n    var n = require('./nodes');\n\n\n  peg$result = peg$startRuleFunction();\n\n  if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n    return peg$result;\n  } else {\n    if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n      peg$fail(peg$endExpectation());\n    }\n\n    throw peg$buildStructuredError(\n      peg$maxFailExpected,\n      peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n      peg$maxFailPos < input.length\n        ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n        : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n    );\n  }\n}\n\nmodule.exports = {\n  SyntaxError: peg$SyntaxError,\n  parse:       peg$parse\n};\n", "import {Assignment, Literal, Node, Variable} from './nodes';\n\n/**\n * Processes a list of statements into a symbol table\n */\nexport default class SymbolTable {\n  constructor(statements, externalSymbols = {}) {\n    this.variables = {};\n    this.symbols = {};\n    this.main = null;\n    this.size = 0;\n\n    this.addExternalSymbols(externalSymbols);\n    this.process(statements);\n  }\n\n  addExternalSymbols(externalSymbols) {\n    for (let key in externalSymbols) {\n      this.variables[key] = new Literal(externalSymbols[key]);\n      this.symbols[key] = externalSymbols[key];\n      this.size++;\n    }\n  }\n\n  process(statements) {\n    for (let statement of statements) {\n      if (statement instanceof Assignment) {\n        this.variables[statement.variable.name] = this.processExpression(statement.expression);\n\n        if (statement.expression instanceof Literal) {\n          this.symbols[statement.variable.name] = statement.expression.value;\n          this.size++;\n        }\n      }\n    }\n\n    this.main = this.variables.main;\n    if (!this.main) {\n      throw new Error('No main variable declaration found');\n    }\n  }\n\n  processExpression(expr) {\n    // Process children\n    for (let key in expr) {\n      if (expr[key] instanceof Node) {\n        expr[key] = this.processExpression(expr[key]);\n      }\n    }\n\n    // Replace variable references with their values\n    if (expr instanceof Variable) {\n      let value = this.variables[expr.name];\n      if (value == null)\n        throw new Error(`Undeclared indentifier ${expr.name}`);\n\n      expr = this.processExpression(value.copy());\n    }\n\n    return expr;\n  }\n}\n", "import {EndMarker, Concatenation, Literal, Tag} from './nodes';\nimport {addAll, equal} from './utils';\n\nconst END_MARKER = new EndMarker;\n\n/**\n * This is an implementation of the direct regular expression to DFA algorithm described\n * in section 3.9.5 of \"Compilers: Principles, Techniques, and Tools\" by <PERSON><PERSON>,\n * <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. http://dragonbook.stanford.edu\n * There is a PDF of the book here:\n * http://www.informatik.uni-bremen.de/agbkb/lehre/ccfl/Material/ALSUdragonbook.pdf\n */\nexport default function buildDFA(root, numSymbols) {\n  root = new Concatenation(root, END_MARKER);\n  root.calcFollowpos();\n\n  let failState = new State(new Set, numSymbols);\n  let initialState = new State(root.firstpos, numSymbols);\n  let dstates = [failState, initialState];\n\n  // while there is an unmarked state S in dstates\n  while (1) {\n    let s = null;\n\n    for (let j = 1; j < dstates.length; j++) {\n      if (!dstates[j].marked) {\n        s = dstates[j];\n        break;\n      }\n    }\n\n    if (s == null) {\n      break;\n    }\n\n    // mark S\n    s.marked = true;\n\n    // for each input symbol a\n    for (let a = 0; a < numSymbols; a++) {\n      // let U be the union of followpos(p) for all\n      //  p in S that correspond to a\n      let u = new Set;\n      for (let p of s.positions) {\n        if (p instanceof Literal && p.value === a) {\n          addAll(u, p.followpos);\n        }\n      }\n\n      if (u.size === 0) {\n        continue;\n      }\n\n      // if U is not in dstates\n      let ux = -1;\n      for (let i = 0; i < dstates.length; i++) {\n        if (equal(u, dstates[i].positions)) {\n          ux = i;\n          break;\n        }\n      }\n\n      if (ux === -1) {\n        // Add U as an unmarked state to dstates\n        dstates.push(new State(u, numSymbols));\n        ux = dstates.length - 1;\n      }\n\n      s.transitions[a] = ux;\n    }\n  }\n\n  return dstates;\n}\n\nclass State {\n  constructor(positions, len) {\n    this.positions = positions;\n    this.transitions = new Uint16Array(len);\n    this.accepting = positions.has(END_MARKER);\n    this.marked = false;\n    this.tags = new Set;\n\n    for (let pos of positions) {\n      if (pos instanceof Tag) {\n        this.tags.add(pos.name);\n      }\n    }\n  }\n}\n", "const INITIAL_STATE = 1;\nconst FAIL_STATE = 0;\n\n/**\n * A StateMachine represents a deterministic finite automaton.\n * It can perform matches over a sequence of values, similar to a regular expression.\n */\nexport default class StateMachine {\n  constructor(dfa) {\n    this.stateTable = dfa.stateTable;\n    this.accepting = dfa.accepting;\n    this.tags = dfa.tags;\n  }\n\n  /**\n   * Returns an iterable object that yields pattern matches over the input sequence.\n   * Matches are of the form [startIndex, endIndex, tags].\n   */\n  match(str) {\n    let self = this;\n    return {\n      *[Symbol.iterator]() {\n        let state = INITIAL_STATE;\n        let startRun = null;\n        let lastAccepting = null;\n        let lastState = null;\n\n        for (let p = 0; p < str.length; p++) {\n          let c = str[p];\n\n          lastState = state;\n          state = self.stateTable[state][c];\n\n          if (state === FAIL_STATE) {\n            // yield the last match if any\n            if (startRun != null && lastAccepting != null && lastAccepting >= startRun) {\n              yield [startRun, lastAccepting, self.tags[lastState]];\n            }\n\n            // reset the state as if we started over from the initial state\n            state = self.stateTable[INITIAL_STATE][c];\n            startRun = null;\n          }\n\n          // start a run if not in the failure state\n          if (state !== FAIL_STATE && startRun == null) {\n            startRun = p;\n          }\n\n          // if accepting, mark the potential match end\n          if (self.accepting[state]) {\n            lastAccepting = p;\n          }\n\n          // reset the state to the initial state if we get into the failure state\n          if (state === FAIL_STATE) {\n            state = INITIAL_STATE;\n          }\n        }\n\n        // yield the last match if any\n        if (startRun != null && lastAccepting != null && lastAccepting >= startRun) {\n          yield [startRun, lastAccepting, self.tags[state]];\n        }\n      }\n    };\n  }\n\n  /**\n   * For each match over the input sequence, action functions matching\n   * the tag definitions in the input pattern are called with the startIndex,\n   * endIndex, and sub-match sequence.\n   */\n  apply(str, actions) {\n    for (let [start, end, tags] of this.match(str)) {\n      for (let tag of tags) {\n        if (typeof actions[tag] === 'function') {\n          actions[tag](start, end, str.slice(start, end + 1));\n        }\n      }\n    }\n  }\n}\n", "import grammar from './grammar';\nimport SymbolTable from './SymbolTable';\nimport buildDFA from './dfa';\nimport StateMachine from './StateMachine';\n\nexport function parse(string, externalSymbols) {\n  let ast = grammar.parse(string);\n  return new SymbolTable(ast, externalSymbols);\n}\n\nexport function build(symbolTable) {\n  let states = buildDFA(symbolTable.main, symbolTable.size);\n\n  return new StateMachine({\n    stateTable: states.map(s => Array.from(s.transitions)),\n    accepting: states.map(s => s.accepting),\n    tags: states.map(s => Array.from(s.tags))\n  });\n}\n\nexport default function compile(string, externalSymbols) {\n  return build(parse(string, externalSymbols));\n}\n"], "names": ["union", "a", "b", "s", "Set", "addAll", "x", "add", "equal", "size", "has", "Node", "constructor", "Object", "defineProperty", "value", "calcFollowpos", "key", "Variable", "name", "copy", "Comment", "Assignment", "variable", "expression", "Alternation", "nullable", "firstpos", "lastpos", "Concatenation", "n", "followpos", "Repeat", "op", "buildRepetition", "min", "max", "Infinity", "Error", "res", "i", "concat", "Leaf", "Literal", "<PERSON><PERSON><PERSON><PERSON>", "Tag", "peg$subclass", "child", "parent", "ctor", "prototype", "peg$SyntaxError", "message", "expected", "found", "location", "captureStackTrace", "buildMessage", "DESCRIBE_EXPECTATION_FNS", "literal", "expectation", "literalEscape", "text", "escapedParts", "parts", "length", "Array", "classEscape", "inverted", "any", "end", "other", "description", "hex", "ch", "charCodeAt", "toString", "toUpperCase", "replace", "describeExpectation", "type", "describeExpected", "descriptions", "j", "sort", "slice", "join", "describeFound", "peg$parse", "input", "options", "peg$FAILED", "peg$startRuleFunctions", "rules", "peg$parserules", "peg$startRuleFunction", "peg$c0", "peg$c1", "peg$c2", "peg$literalExpectation", "peg$c3", "peg$c4", "peg$classExpectation", "peg$c5", "peg$c6", "peg$c7", "v", "peg$c8", "peg$c9", "peg$c10", "peg$c11", "peg$c12", "e", "peg$c13", "peg$c14", "peg$c15", "peg$c16", "peg$c17", "peg$c18", "peg$c19", "peg$c20", "t", "peg$c21", "peg$c22", "peg$c23", "peg$c24", "peg$c25", "peg$c26", "peg$c27", "peg$c28", "peg$c29", "peg$c30", "peg$c31", "peg$c32", "peg$c33", "peg$c34", "m", "peg$c35", "peg$c36", "peg$c37", "peg$c38", "peg$c39", "peg$c40", "peg$c41", "peg$c42", "peg$c43", "peg$c44", "peg$c45", "peg$c46", "peg$c47", "peg$c48", "peg$c49", "peg$c50", "peg$c51", "peg$c52", "peg$c53", "peg$c54", "num", "parseInt", "peg$c55", "peg$c56", "peg$currPos", "peg$savedPos", "peg$posDetailsCache", "line", "column", "peg$maxFailPos", "peg$maxFailExpected", "peg$silentFails", "peg$result", "startRule", "ignoreCase", "peg$endExpectation", "peg$computePosDetails", "pos", "details", "p", "peg$computeLocation", "startPos", "endPos", "startPosDetails", "endPosDetails", "start", "offset", "peg$fail", "push", "peg$buildStructuredError", "s0", "s1", "peg$parsestatement", "s2", "peg$parsestatement_type", "peg$parse_", "peg$parseassignment", "peg$parsecomment", "s3", "test", "char<PERSON>t", "s4", "s5", "s6", "s7", "peg$parsevariable", "peg$parsealternation", "peg$parsename", "peg$parseconcatenation", "peg$parserepeat", "peg$parseterm", "peg$parsenumber", "peg$parsename_start_char", "peg$parsename_char", "require$$0", "SyntaxError", "parse", "SymbolTable", "statements", "externalSymbols", "variables", "symbols", "main", "addExternalSymbols", "process", "statement", "processExpression", "expr", "END_MARKER", "buildDFA", "root", "numSymbols", "failState", "State", "initialState", "dstates", "marked", "u", "positions", "ux", "transitions", "len", "Uint16Array", "accepting", "tags", "INITIAL_STATE", "FAIL_STATE", "StateMachine", "dfa", "stateTable", "match", "str", "self", "Symbol", "iterator", "state", "startRun", "lastAccepting", "lastState", "c", "apply", "actions", "tag", "string", "ast", "grammar", "build", "symbolTable", "states", "map", "from", "compile"], "mappings": ";;;;AAAA;;;AAGA,AAAO,SAASA,KAAT,CAAeC,CAAf,EAAkBC,CAAlB,EAAqB;MACtBC,CAAC,GAAG,IAAIC,GAAJ,CAAQH,CAAR,CAAR;EACAI,MAAM,CAACF,CAAD,EAAID,CAAJ,CAAN;SACOC,CAAP;;;;;;AAMF,AAAO,SAASE,MAAT,CAAgBJ,CAAhB,EAAmBC,CAAnB,EAAsB;OACtB,IAAII,CAAT,IAAcJ,CAAd,EAAiB;IACfD,CAAC,CAACM,GAAF,CAAMD,CAAN;;;;;;;AAOJ,AAAO,SAASE,KAAT,CAAeP,CAAf,EAAkBC,CAAlB,EAAqB;MACtBD,CAAC,KAAKC,CAAV,EACE,OAAO,IAAP;MAEED,CAAC,CAACQ,IAAF,KAAWP,CAAC,CAACO,IAAjB,EACE,OAAO,KAAP;;OAEG,IAAIH,CAAT,IAAcL,CAAd,EAAiB;QACX,CAACC,CAAC,CAACQ,GAAF,CAAMJ,CAAN,CAAL,EAAe;aACN,KAAP;;;;SAIG,IAAP;;;AChCF;;;;AAGA,AAAO,MAAMK,IAAN,CAAW;EAChBC,WAAW,GAAG;IACZC,MAAM,CAACC,cAAP,CAAsB,IAAtB,EAA4B,WAA5B,EAAyC;MAACC,KAAK,EAAE,IAAIX,GAAJ;KAAjD;;;EAGFY,aAAa,GAAG;SACT,IAAIC,GAAT,IAAgB,IAAhB,EAAsB;UAChB,KAAKA,GAAL,aAAqBN,IAAzB,EAA+B;aACxBM,GAAL,EAAUD,aAAV;;;;;;;;;;AASR,AAAO,MAAME,QAAN,SAAuBP,IAAvB,CAA4B;EACjCC,WAAW,CAACO,IAAD,EAAO;;SAEXA,IAAL,GAAYA,IAAZ;;;EAGFC,IAAI,GAAG;WACE,IAAIF,QAAJ,CAAa,KAAKC,IAAlB,CAAP;;;;;;;;AAOJ,AAAO,MAAME,OAAN,SAAsBV,IAAtB,CAA2B;EAChCC,WAAW,CAACG,KAAD,EAAQ;;SAEZA,KAAL,GAAaA,KAAb;;;;;;;;;AAQJ,AAAO,MAAMO,UAAN,SAAyBX,IAAzB,CAA8B;EACnCC,WAAW,CAACW,QAAD,EAAWC,UAAX,EAAuB;;SAE3BD,QAAL,GAAgBA,QAAhB;SACKC,UAAL,GAAkBA,UAAlB;;;;;;;;;AAQJ,AAAO,MAAMC,WAAN,SAA0Bd,IAA1B,CAA+B;EACpCC,WAAW,CAACX,CAAD,EAAIC,CAAJ,EAAO;;SAEXD,CAAL,GAASA,CAAT;SACKC,CAAL,GAASA,CAAT;;;MAGEwB,QAAJ,GAAe;WACN,KAAKzB,CAAL,CAAOyB,QAAP,IAAmB,KAAKxB,CAAL,CAAOwB,QAAjC;;;MAGEC,QAAJ,GAAe;WACN3B,KAAK,CAAC,KAAKC,CAAL,CAAO0B,QAAR,EAAkB,KAAKzB,CAAL,CAAOyB,QAAzB,CAAZ;;;MAGEC,OAAJ,GAAc;WACL5B,KAAK,CAAC,KAAKC,CAAL,CAAO2B,OAAR,EAAiB,KAAK1B,CAAL,CAAO0B,OAAxB,CAAZ;;;EAGFR,IAAI,GAAG;WACE,IAAIK,WAAJ,CAAgB,KAAKxB,CAAL,CAAOmB,IAAP,EAAhB,EAA+B,KAAKlB,CAAL,CAAOkB,IAAP,EAA/B,CAAP;;;;;;;;;AAQJ,AAAO,MAAMS,aAAN,SAA4BlB,IAA5B,CAAiC;EACtCC,WAAW,CAACX,CAAD,EAAIC,CAAJ,EAAO;;SAEXD,CAAL,GAASA,CAAT;SACKC,CAAL,GAASA,CAAT;;;MAGEwB,QAAJ,GAAe;WACN,KAAKzB,CAAL,CAAOyB,QAAP,IAAmB,KAAKxB,CAAL,CAAOwB,QAAjC;;;MAGEC,QAAJ,GAAe;QACTxB,CAAC,GAAG,KAAKF,CAAL,CAAO0B,QAAf;;QACI,KAAK1B,CAAL,CAAOyB,QAAX,EAAqB;MACnBvB,CAAC,GAAGH,KAAK,CAACG,CAAD,EAAI,KAAKD,CAAL,CAAOyB,QAAX,CAAT;;;WAGKxB,CAAP;;;MAGEyB,OAAJ,GAAc;QACRzB,CAAC,GAAG,KAAKD,CAAL,CAAO0B,OAAf;;QACI,KAAK1B,CAAL,CAAOwB,QAAX,EAAqB;MACnBvB,CAAC,GAAGH,KAAK,CAACG,CAAD,EAAI,KAAKF,CAAL,CAAO2B,OAAX,CAAT;;;WAGKzB,CAAP;;;EAGFa,aAAa,GAAG;UACRA,aAAN;;SACK,IAAIc,CAAT,IAAc,KAAK7B,CAAL,CAAO2B,OAArB,EAA8B;MAC5BvB,MAAM,CAACyB,CAAC,CAACC,SAAH,EAAc,KAAK7B,CAAL,CAAOyB,QAArB,CAAN;;;;EAIJP,IAAI,GAAG;WACE,IAAIS,aAAJ,CAAkB,KAAK5B,CAAL,CAAOmB,IAAP,EAAlB,EAAiC,KAAKlB,CAAL,CAAOkB,IAAP,EAAjC,CAAP;;;;;;;;;AAQJ,AAAO,MAAMY,MAAN,SAAqBrB,IAArB,CAA0B;EAC/BC,WAAW,CAACY,UAAD,EAAaS,EAAb,EAAiB;;SAErBT,UAAL,GAAkBA,UAAlB;SACKS,EAAL,GAAUA,EAAV;;;MAGEP,QAAJ,GAAe;WACN,KAAKO,EAAL,KAAY,GAAZ,IAAmB,KAAKA,EAAL,KAAY,GAAtC;;;MAGEN,QAAJ,GAAe;WACN,KAAKH,UAAL,CAAgBG,QAAvB;;;MAGEC,OAAJ,GAAc;WACL,KAAKJ,UAAL,CAAgBI,OAAvB;;;EAGFZ,aAAa,GAAG;UACRA,aAAN;;QACI,KAAKiB,EAAL,KAAY,GAAZ,IAAmB,KAAKA,EAAL,KAAY,GAAnC,EAAwC;WACjC,IAAIH,CAAT,IAAc,KAAKF,OAAnB,EAA4B;QAC1BvB,MAAM,CAACyB,CAAC,CAACC,SAAH,EAAc,KAAKJ,QAAnB,CAAN;;;;;EAKNP,IAAI,GAAG;WACE,IAAIY,MAAJ,CAAW,KAAKR,UAAL,CAAgBJ,IAAhB,EAAX,EAAmC,KAAKa,EAAxC,CAAP;;;;AAIJ,AAAO,SAASC,eAAT,CAAyBV,UAAzB,EAAqCW,GAAG,GAAG,CAA3C,EAA8CC,GAAG,GAAGC,QAApD,EAA8D;MAC/DF,GAAG,GAAG,CAAN,IAAWA,GAAG,GAAGC,GAArB,EAA0B;UAClB,IAAIE,KAAJ,qCAAuCH,GAAvC,cAA8CC,GAA9C,EAAN;;;MAGEG,GAAG,GAAG,IAAV;;OACK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,GAApB,EAAyBK,CAAC,EAA1B,EAA8B;IAC5BD,GAAG,GAAGE,MAAM,CAACF,GAAD,EAAMf,UAAU,CAACJ,IAAX,EAAN,CAAZ;;;MAGEgB,GAAG,KAAKC,QAAZ,EAAsB;IACpBE,GAAG,GAAGE,MAAM,CAACF,GAAD,EAAM,IAAIP,MAAJ,CAAWR,UAAU,CAACJ,IAAX,EAAX,EAA8B,GAA9B,CAAN,CAAZ;GADF,MAEO;SACA,IAAIoB,EAAC,GAAGL,GAAb,EAAkBK,EAAC,GAAGJ,GAAtB,EAA2BI,EAAC,EAA5B,EAAgC;MAC9BD,GAAG,GAAGE,MAAM,CAACF,GAAD,EAAM,IAAIP,MAAJ,CAAWR,UAAU,CAACJ,IAAX,EAAX,EAA8B,GAA9B,CAAN,CAAZ;;;;SAIGmB,GAAP;;;AAGF,SAASE,MAAT,CAAgBxC,CAAhB,EAAmBC,CAAnB,EAAsB;MAChB,CAACD,CAAL,EAAQ;WACCC,CAAP;;;SAGK,IAAI2B,aAAJ,CAAkB5B,CAAlB,EAAqBC,CAArB,CAAP;;;;;;;AAMF,MAAMwC,IAAN,SAAmB/B,IAAnB,CAAwB;MAClBe,QAAJ,GAAe;WACN,KAAP;;;MAGEC,QAAJ,GAAe;WACN,IAAIvB,GAAJ,CAAQ,CAAC,IAAD,CAAR,CAAP;;;MAGEwB,OAAJ,GAAc;WACL,IAAIxB,GAAJ,CAAQ,CAAC,IAAD,CAAR,CAAP;;;;;;;;;AAOJ,AAAO,MAAMuC,OAAN,SAAsBD,IAAtB,CAA2B;EAChC9B,WAAW,CAACG,KAAD,EAAQ;;SAEZA,KAAL,GAAaA,KAAb;;;EAGFK,IAAI,GAAG;WACE,IAAIuB,OAAJ,CAAY,KAAK5B,KAAjB,CAAP;;;;;;;;AAOJ,AAAO,MAAM6B,SAAN,SAAwBF,IAAxB,CAA6B;;;;;;AAMpC,AAAO,MAAMG,GAAN,SAAkBH,IAAlB,CAAuB;EAC5B9B,WAAW,CAACO,IAAD,EAAO;;SAEXA,IAAL,GAAYA,IAAZ;;;MAGEO,QAAJ,GAAe;WACN,IAAP;;;EAGFN,IAAI,GAAG;WACE,IAAIyB,GAAJ,CAAQ,KAAK1B,IAAb,CAAP;;;;;;;;;;;;;;;;;;;AC7OJ,SAAS2B,YAAT,CAAsBC,KAAtB,EAA6BC,MAA7B,EAAqC;WAC1BC,IAAT,GAAgB;SAAOrC,WAAL,GAAmBmC,KAAnB;;;EAClBE,IAAI,CAACC,SAAL,GAAiBF,MAAM,CAACE,SAAxB;EACAH,KAAK,CAACG,SAAN,GAAkB,IAAID,IAAJ,EAAlB;;;AAGF,SAASE,eAAT,CAAyBC,OAAzB,EAAkCC,QAAlC,EAA4CC,KAA5C,EAAmDC,QAAnD,EAA6D;OACtDH,OAAL,GAAgBA,OAAhB;OACKC,QAAL,GAAgBA,QAAhB;OACKC,KAAL,GAAgBA,KAAhB;OACKC,QAAL,GAAgBA,QAAhB;OACKpC,IAAL,GAAgB,aAAhB;;MAEI,OAAOmB,KAAK,CAACkB,iBAAb,KAAmC,UAAvC,EAAmD;IACjDlB,KAAK,CAACkB,iBAAN,CAAwB,IAAxB,EAA8BL,eAA9B;;;;AAIJL,YAAY,CAACK,eAAD,EAAkBb,KAAlB,CAAZ;;AAEAa,eAAe,CAACM,YAAhB,GAA+B,UAASJ,QAAT,EAAmBC,KAAnB,EAA0B;MACnDI,wBAAwB,GAAG;IACzBC,OAAO,EAAE,UAASC,WAAT,EAAsB;aACtB,OAAOC,aAAa,CAACD,WAAW,CAACE,IAAb,CAApB,GAAyC,IAAhD;KAFuB;aAKhB,UAASF,WAAT,EAAsB;UACzBG,YAAY,GAAG,EAAnB;UACIvB,CADJ;;WAGKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGoB,WAAW,CAACI,KAAZ,CAAkBC,MAAlC,EAA0CzB,CAAC,EAA3C,EAA+C;QAC7CuB,YAAY,IAAIH,WAAW,CAACI,KAAZ,CAAkBxB,CAAlB,aAAgC0B,KAAhC,GACZC,WAAW,CAACP,WAAW,CAACI,KAAZ,CAAkBxB,CAAlB,EAAqB,CAArB,CAAD,CAAX,GAAuC,GAAvC,GAA6C2B,WAAW,CAACP,WAAW,CAACI,KAAZ,CAAkBxB,CAAlB,EAAqB,CAArB,CAAD,CAD5C,GAEZ2B,WAAW,CAACP,WAAW,CAACI,KAAZ,CAAkBxB,CAAlB,CAAD,CAFf;;;aAKK,OAAOoB,WAAW,CAACQ,QAAZ,GAAuB,GAAvB,GAA6B,EAApC,IAA0CL,YAA1C,GAAyD,GAAhE;KAfuB;IAkBzBM,GAAG,EAAE,UAAST,WAAT,EAAsB;aAClB,eAAP;KAnBuB;IAsBzBU,GAAG,EAAE,UAASV,WAAT,EAAsB;aAClB,cAAP;KAvBuB;IA0BzBW,KAAK,EAAE,UAASX,WAAT,EAAsB;aACpBA,WAAW,CAACY,WAAnB;;GA3BR;;WA+BSC,GAAT,CAAaC,EAAb,EAAiB;WACRA,EAAE,CAACC,UAAH,CAAc,CAAd,EAAiBC,QAAjB,CAA0B,EAA1B,EAA8BC,WAA9B,EAAP;;;WAGOhB,aAAT,CAAuB1D,CAAvB,EAA0B;WACjBA,CAAC,CACL2E,OADI,CACI,KADJ,EACW,MADX,EAEJA,OAFI,CAEI,IAFJ,EAEW,KAFX,EAGJA,OAHI,CAGI,KAHJ,EAGW,KAHX,EAIJA,OAJI,CAII,KAJJ,EAIW,KAJX,EAKJA,OALI,CAKI,KALJ,EAKW,KALX,EAMJA,OANI,CAMI,KANJ,EAMW,KANX,EAOJA,OAPI,CAOI,cAPJ,EAO6B,UAASJ,EAAT,EAAa;aAAS,SAASD,GAAG,CAACC,EAAD,CAAnB;KAP5C,EAQJI,OARI,CAQI,uBARJ,EAQ6B,UAASJ,EAAT,EAAa;aAAS,QAASD,GAAG,CAACC,EAAD,CAAnB;KAR5C,CAAP;;;WAWOP,WAAT,CAAqBhE,CAArB,EAAwB;WACfA,CAAC,CACL2E,OADI,CACI,KADJ,EACW,MADX,EAEJA,OAFI,CAEI,KAFJ,EAEW,KAFX,EAGJA,OAHI,CAGI,KAHJ,EAGW,KAHX,EAIJA,OAJI,CAII,IAJJ,EAIW,KAJX,EAKJA,OALI,CAKI,KALJ,EAKW,KALX,EAMJA,OANI,CAMI,KANJ,EAMW,KANX,EAOJA,OAPI,CAOI,KAPJ,EAOW,KAPX,EAQJA,OARI,CAQI,KARJ,EAQW,KARX,EASJA,OATI,CASI,cATJ,EAS6B,UAASJ,EAAT,EAAa;aAAS,SAASD,GAAG,CAACC,EAAD,CAAnB;KAT5C,EAUJI,OAVI,CAUI,uBAVJ,EAU6B,UAASJ,EAAT,EAAa;aAAS,QAASD,GAAG,CAACC,EAAD,CAAnB;KAV5C,CAAP;;;WAaOK,mBAAT,CAA6BnB,WAA7B,EAA0C;WACjCF,wBAAwB,CAACE,WAAW,CAACoB,IAAb,CAAxB,CAA2CpB,WAA3C,CAAP;;;WAGOqB,gBAAT,CAA0B5B,QAA1B,EAAoC;QAC9B6B,YAAY,GAAG,IAAIhB,KAAJ,CAAUb,QAAQ,CAACY,MAAnB,CAAnB;QACIzB,CADJ;QACO2C,CADP;;SAGK3C,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGa,QAAQ,CAACY,MAAzB,EAAiCzB,CAAC,EAAlC,EAAsC;MACpC0C,YAAY,CAAC1C,CAAD,CAAZ,GAAkBuC,mBAAmB,CAAC1B,QAAQ,CAACb,CAAD,CAAT,CAArC;;;IAGF0C,YAAY,CAACE,IAAb;;QAEIF,YAAY,CAACjB,MAAb,GAAsB,CAA1B,EAA6B;WACtBzB,CAAC,GAAG,CAAJ,EAAO2C,CAAC,GAAG,CAAhB,EAAmB3C,CAAC,GAAG0C,YAAY,CAACjB,MAApC,EAA4CzB,CAAC,EAA7C,EAAiD;YAC3C0C,YAAY,CAAC1C,CAAC,GAAG,CAAL,CAAZ,KAAwB0C,YAAY,CAAC1C,CAAD,CAAxC,EAA6C;UAC3C0C,YAAY,CAACC,CAAD,CAAZ,GAAkBD,YAAY,CAAC1C,CAAD,CAA9B;UACA2C,CAAC;;;;MAGLD,YAAY,CAACjB,MAAb,GAAsBkB,CAAtB;;;YAGMD,YAAY,CAACjB,MAArB;WACO,CAAL;eACSiB,YAAY,CAAC,CAAD,CAAnB;;WAEG,CAAL;eACSA,YAAY,CAAC,CAAD,CAAZ,GAAkB,MAAlB,GAA2BA,YAAY,CAAC,CAAD,CAA9C;;;eAGOA,YAAY,CAACG,KAAb,CAAmB,CAAnB,EAAsB,CAAC,CAAvB,EAA0BC,IAA1B,CAA+B,IAA/B,IACH,OADG,GAEHJ,YAAY,CAACA,YAAY,CAACjB,MAAb,GAAsB,CAAvB,CAFhB;;;;WAMGsB,aAAT,CAAuBjC,KAAvB,EAA8B;WACrBA,KAAK,GAAG,OAAOO,aAAa,CAACP,KAAD,CAApB,GAA8B,IAAjC,GAAwC,cAApD;;;SAGK,cAAc2B,gBAAgB,CAAC5B,QAAD,CAA9B,GAA2C,OAA3C,GAAqDkC,aAAa,CAACjC,KAAD,CAAlE,GAA4E,SAAnF;CAxGF;;AA2GA,SAASkC,SAAT,CAAmBC,KAAnB,EAA0BC,OAA1B,EAAmC;EACjCA,OAAO,GAAGA,OAAO,KAAK,KAAK,CAAjB,GAAqBA,OAArB,GAA+B,EAAzC;;MAEIC,UAAU,GAAG,EAAjB;MAEIC,sBAAsB,GAAG;IAAEC,KAAK,EAAEC;GAFtC;MAGIC,qBAAqB,GAAID,cAH7B;MAKIE,MAAM,GAAG,UAAS7F,CAAT,EAAY;WAASA,CAAP;GAL3B;MAMI8F,MAAM,GAAG,GANb;MAOIC,MAAM,GAAGC,sBAAsB,CAAC,GAAD,EAAM,KAAN,CAPnC;MAQIC,MAAM,GAAG,UARb;MASIC,MAAM,GAAGC,oBAAoB,CAAC,CAAC,IAAD,EAAO,IAAP,CAAD,EAAe,IAAf,EAAqB,KAArB,CATjC;MAUIC,MAAM,GAAG,SAVb;MAWIC,MAAM,GAAGF,oBAAoB,CAAC,CAAC,IAAD,EAAO,IAAP,CAAD,EAAe,KAAf,EAAsB,KAAtB,CAXjC;MAYIG,MAAM,GAAG,UAASC,CAAT,EAAY;WAAS,IAAI5E,CAAC,CAACT,OAAN,CAAcqF,CAAC,CAACpB,IAAF,CAAO,EAAP,CAAd,CAAP;GAZ3B;MAaIqB,MAAM,GAAG,GAbb;MAcIC,MAAM,GAAGT,sBAAsB,CAAC,GAAD,EAAM,KAAN,CAdnC;MAeIU,OAAO,GAAG,GAfd;MAgBIC,OAAO,GAAGX,sBAAsB,CAAC,GAAD,EAAM,KAAN,CAhBpC;MAiBIY,OAAO,GAAG,UAASL,CAAT,EAAYM,CAAZ,EAAe;WAAS,IAAIlF,CAAC,CAACR,UAAN,CAAiBoF,CAAjB,EAAoBM,CAApB,CAAP;GAjB/B;MAkBIC,OAAO,GAAG,UAASP,CAAT,EAAY;WAAS,IAAI5E,CAAC,CAACZ,QAAN,CAAewF,CAAf,CAAP;GAlB5B;MAmBIQ,OAAO,GAAG,GAnBd;MAoBIC,OAAO,GAAGhB,sBAAsB,CAAC,GAAD,EAAM,KAAN,CApBpC;MAqBIiB,OAAO,GAAG,UAASnH,CAAT,EAAYC,CAAZ,EAAe;WAAS,IAAI4B,CAAC,CAACL,WAAN,CAAkBxB,CAAlB,EAAqBC,CAArB,CAAP;GArB/B;MAsBImH,OAAO,GAAG,UAASpH,CAAT,EAAYC,CAAZ,EAAe;WAAS,IAAI4B,CAAC,CAACD,aAAN,CAAoB5B,CAApB,EAAuBC,CAAvB,CAAP;GAtB/B;MAuBIoH,OAAO,GAAG,GAvBd;MAwBIC,OAAO,GAAGpB,sBAAsB,CAAC,GAAD,EAAM,KAAN,CAxBpC;MAyBIqB,OAAO,GAAG,UAASC,CAAT,EAAYT,CAAZ,EAAe;WAAS,IAAIlF,CAAC,CAACD,aAAN,CAAoBmF,CAApB,EAAuB,IAAIlF,CAAC,CAACe,GAAN,CAAU4E,CAAV,CAAvB,CAAP;GAzB/B;MA0BIC,OAAO,GAAG,GA1Bd;MA2BIC,OAAO,GAAGxB,sBAAsB,CAAC,GAAD,EAAM,KAAN,CA3BpC;MA4BIyB,OAAO,GAAG,UAASH,CAAT,EAAY;WAAS,IAAI3F,CAAC,CAACE,MAAN,CAAayF,CAAb,EAAgB,GAAhB,CAAP;GA5B5B;MA6BII,OAAO,GAAG,GA7Bd;MA8BIC,OAAO,GAAG3B,sBAAsB,CAAC,GAAD,EAAM,KAAN,CA9BpC;MA+BI4B,OAAO,GAAG,UAASN,CAAT,EAAY;WAAS,IAAI3F,CAAC,CAACE,MAAN,CAAayF,CAAb,EAAgB,GAAhB,CAAP;GA/B5B;MAgCIO,OAAO,GAAG,GAhCd;MAiCIC,OAAO,GAAG9B,sBAAsB,CAAC,GAAD,EAAM,KAAN,CAjCpC;MAkCI+B,OAAO,GAAG,UAAST,CAAT,EAAY;WAAS,IAAI3F,CAAC,CAACE,MAAN,CAAayF,CAAb,EAAgB,GAAhB,CAAP;GAlC5B;MAmCIU,OAAO,GAAG,GAnCd;MAoCIC,OAAO,GAAGjC,sBAAsB,CAAC,GAAD,EAAM,KAAN,CApCpC;MAqCIkC,OAAO,GAAG,GArCd;MAsCIC,OAAO,GAAGnC,sBAAsB,CAAC,GAAD,EAAM,KAAN,CAtCpC;MAuCIoC,OAAO,GAAG,UAASd,CAAT,EAAYe,CAAZ,EAAe;WAAS1G,CAAC,CAACI,eAAF,CAAkBuF,CAAlB,EAAqBe,CAArB,EAAwBA,CAAxB,CAAP;GAvC/B;MAwCIC,OAAO,GAAG,GAxCd;MAyCIC,OAAO,GAAGvC,sBAAsB,CAAC,GAAD,EAAM,KAAN,CAzCpC;MA0CIwC,OAAO,GAAG,UAASlB,CAAT,EAAYtF,GAAZ,EAAiB;WAASL,CAAC,CAACI,eAAF,CAAkBuF,CAAlB,EAAqBtF,GAArB,EAA0BE,QAA1B,CAAP;GA1CjC;MA2CIuG,OAAO,GAAG,UAASnB,CAAT,EAAYrF,GAAZ,EAAiB;WAASN,CAAC,CAACI,eAAF,CAAkBuF,CAAlB,EAAqB,CAArB,EAAwBrF,GAAxB,CAAP;GA3CjC;MA4CIyG,OAAO,GAAG,UAASpB,CAAT,EAAYtF,GAAZ,EAAiBC,GAAjB,EAAsB;WAASN,CAAC,CAACI,eAAF,CAAkBuF,CAAlB,EAAqBtF,GAArB,EAA0BC,GAA1B,CAAP;GA5CtC;MA6CI0G,OAAO,GAAG,UAASxI,CAAT,EAAY;WAAS,IAAIwB,CAAC,CAACa,OAAN,CAAcrC,CAAd,CAAP;GA7C5B;MA8CIyI,OAAO,GAAG,GA9Cd;MA+CIC,OAAO,GAAG7C,sBAAsB,CAAC,GAAD,EAAM,KAAN,CA/CpC;MAgDI8C,OAAO,GAAG,GAhDd;MAiDIC,OAAO,GAAG/C,sBAAsB,CAAC,GAAD,EAAM,KAAN,CAjDpC;MAkDIgD,OAAO,GAAG,UAASnC,CAAT,EAAY;WAASA,CAAP;GAlD5B;MAmDIoC,AACAC,OAAO,GAAG,UAASpJ,CAAT,EAAYC,CAAZ,EAAe;WAASD,CAAC,GAAGC,CAAC,CAACoF,IAAF,CAAO,EAAP,CAAX;GApD/B;MAqDIgE,OAAO,GAAG,GArDd;MAsDIC,OAAO,GAAGpD,sBAAsB,CAAC,GAAD,EAAM,KAAN,CAtDpC;MAuDIqD,OAAO,GAAG,WAvDd;MAwDIC,OAAO,GAAGnD,oBAAoB,CAAC,CAAC,CAAC,GAAD,EAAM,GAAN,CAAD,EAAa,CAAC,GAAD,EAAM,GAAN,CAAb,CAAD,EAA2B,KAA3B,EAAkC,KAAlC,CAxDlC;MAyDIoD,OAAO,GAAG,QAzDd;MA0DIC,OAAO,GAAGrD,oBAAoB,CAAC,CAAC,CAAC,GAAD,EAAM,GAAN,CAAD,CAAD,EAAe,KAAf,EAAsB,KAAtB,CA1DlC;MA2DIsD,OAAO,GAAG,UAASC,GAAT,EAAc;WAASC,QAAQ,CAACD,GAAG,CAACvE,IAAJ,CAAS,EAAT,CAAD,CAAf;GA3D9B;MA4DIyE,OAAO,GAAG,YA5Dd;MA6DIC,OAAO,GAAG1D,oBAAoB,CAAC,CAAC,GAAD,EAAM,IAAN,EAAY,IAAZ,EAAkB,IAAlB,CAAD,EAA0B,KAA1B,EAAiC,KAAjC,CA7DlC;MA+DI2D,WAAW,GAAY,CA/D3B;MAgEIC,AACAC,mBAAmB,GAAI,CAAC;IAAEC,IAAI,EAAE,CAAR;IAAWC,MAAM,EAAE;GAApB,CAjE3B;MAkEIC,cAAc,GAAS,CAlE3B;MAmEIC,mBAAmB,GAAI,EAnE3B;MAoEIC,AAEAC,UAtEJ;;MAwEI,eAAe/E,OAAnB,EAA4B;QACtB,EAAEA,OAAO,CAACgF,SAAR,IAAqB9E,sBAAvB,CAAJ,EAAoD;YAC5C,IAAItD,KAAJ,CAAU,qCAAqCoD,OAAO,CAACgF,SAA7C,GAAyD,KAAnE,CAAN;;;IAGF3E,qBAAqB,GAAGH,sBAAsB,CAACF,OAAO,CAACgF,SAAT,CAA9C;;;WA2BOvE,sBAAT,CAAgCrC,IAAhC,EAAsC6G,UAAtC,EAAkD;WACzC;MAAE3F,IAAI,EAAE,SAAR;MAAmBlB,IAAI,EAAEA,IAAzB;MAA+B6G,UAAU,EAAEA;KAAlD;;;WAGOrE,oBAAT,CAA8BtC,KAA9B,EAAqCI,QAArC,EAA+CuG,UAA/C,EAA2D;WAClD;MAAE3F,IAAI,EAAE,OAAR;MAAiBhB,KAAK,EAAEA,KAAxB;MAA+BI,QAAQ,EAAEA,QAAzC;MAAmDuG,UAAU,EAAEA;KAAtE;;;WAOOC,kBAAT,GAA8B;WACrB;MAAE5F,IAAI,EAAE;KAAf;;;WAOO6F,qBAAT,CAA+BC,GAA/B,EAAoC;QAC9BC,OAAO,GAAGZ,mBAAmB,CAACW,GAAD,CAAjC;QAAwCE,CAAxC;;QAEID,OAAJ,EAAa;aACJA,OAAP;KADF,MAEO;MACLC,CAAC,GAAGF,GAAG,GAAG,CAAV;;aACO,CAACX,mBAAmB,CAACa,CAAD,CAA3B,EAAgC;QAC9BA,CAAC;;;MAGHD,OAAO,GAAGZ,mBAAmB,CAACa,CAAD,CAA7B;MACAD,OAAO,GAAG;QACRX,IAAI,EAAIW,OAAO,CAACX,IADR;QAERC,MAAM,EAAEU,OAAO,CAACV;OAFlB;;aAKOW,CAAC,GAAGF,GAAX,EAAgB;YACVrF,KAAK,CAACd,UAAN,CAAiBqG,CAAjB,MAAwB,EAA5B,EAAgC;UAC9BD,OAAO,CAACX,IAAR;UACAW,OAAO,CAACV,MAAR,GAAiB,CAAjB;SAFF,MAGO;UACLU,OAAO,CAACV,MAAR;;;QAGFW,CAAC;;;MAGHb,mBAAmB,CAACW,GAAD,CAAnB,GAA2BC,OAA3B;aACOA,OAAP;;;;WAIKE,mBAAT,CAA6BC,QAA7B,EAAuCC,MAAvC,EAA+C;QACzCC,eAAe,GAAGP,qBAAqB,CAACK,QAAD,CAA3C;QACIG,aAAa,GAAKR,qBAAqB,CAACM,MAAD,CAD3C;WAGO;MACLG,KAAK,EAAE;QACLC,MAAM,EAAEL,QADH;QAELd,IAAI,EAAIgB,eAAe,CAAChB,IAFnB;QAGLC,MAAM,EAAEe,eAAe,CAACf;OAJrB;MAML/F,GAAG,EAAE;QACHiH,MAAM,EAAEJ,MADL;QAEHf,IAAI,EAAIiB,aAAa,CAACjB,IAFnB;QAGHC,MAAM,EAAEgB,aAAa,CAAChB;;KAT1B;;;WAcOmB,QAAT,CAAkBnI,QAAlB,EAA4B;QACtB4G,WAAW,GAAGK,cAAlB,EAAkC;;;;QAE9BL,WAAW,GAAGK,cAAlB,EAAkC;MAChCA,cAAc,GAAGL,WAAjB;MACAM,mBAAmB,GAAG,EAAtB;;;IAGFA,mBAAmB,CAACkB,IAApB,CAAyBpI,QAAzB;;;WAOOqI,wBAAT,CAAkCrI,QAAlC,EAA4CC,KAA5C,EAAmDC,QAAnD,EAA6D;WACpD,IAAIJ,eAAJ,CACLA,eAAe,CAACM,YAAhB,CAA6BJ,QAA7B,EAAuCC,KAAvC,CADK,EAELD,QAFK,EAGLC,KAHK,EAILC,QAJK,CAAP;;;WAQOuC,cAAT,GAA0B;QACpB6F,EAAJ,EAAQC,EAAR;IAEAD,EAAE,GAAG,EAAL;IACAC,EAAE,GAAGC,kBAAkB,EAAvB;;QACID,EAAE,KAAKjG,UAAX,EAAuB;aACdiG,EAAE,KAAKjG,UAAd,EAA0B;QACxBgG,EAAE,CAACF,IAAH,CAAQG,EAAR;QACAA,EAAE,GAAGC,kBAAkB,EAAvB;;KAHJ,MAKO;MACLF,EAAE,GAAGhG,UAAL;;;WAGKgG,EAAP;;;WAGOE,kBAAT,GAA8B;QACxBF,EAAJ,EAAQC,EAAR,EAAYE,EAAZ;IAEAH,EAAE,GAAG1B,WAAL;IACA2B,EAAE,GAAGG,uBAAuB,EAA5B;;QACIH,EAAE,KAAKjG,UAAX,EAAuB;MACrBmG,EAAE,GAAGE,UAAU,EAAf;;UACIF,EAAE,KAAKnG,UAAX,EAAuB;AACrBuE,AACA0B,QAAAA,EAAE,GAAG5F,MAAM,CAAC4F,EAAD,CAAX;QACAD,EAAE,GAAGC,EAAL;OAHF,MAIO;QACL3B,WAAW,GAAG0B,EAAd;QACAA,EAAE,GAAGhG,UAAL;;KARJ,MAUO;MACLsE,WAAW,GAAG0B,EAAd;MACAA,EAAE,GAAGhG,UAAL;;;WAGKgG,EAAP;;;WAGOI,uBAAT,GAAmC;QAC7BJ,EAAJ;IAEAA,EAAE,GAAGM,mBAAmB,EAAxB;;QACIN,EAAE,KAAKhG,UAAX,EAAuB;MACrBgG,EAAE,GAAGO,gBAAgB,EAArB;;;WAGKP,EAAP;;;WAGOO,gBAAT,GAA4B;QACtBP,EAAJ,EAAQC,EAAR,EAAYE,EAAZ,EAAgBK,EAAhB;IAEAR,EAAE,GAAG1B,WAAL;;QACIxE,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;MACxC2B,EAAE,GAAG3F,MAAL;MACAgE,WAAW;KAFb,MAGO;MACL2B,EAAE,GAAGjG,UAAL;;MAC2B;QAAE6F,QAAQ,CAACtF,MAAD,CAAR;;;;QAE3B0F,EAAE,KAAKjG,UAAX,EAAuB;MACrBmG,EAAE,GAAG,EAAL;;UACI1F,MAAM,CAACgG,IAAP,CAAY3G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAZ,CAAJ,EAA4C;QAC1CkC,EAAE,GAAG1G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAL;QACAA,WAAW;OAFb,MAGO;QACLkC,EAAE,GAAGxG,UAAL;;QAC2B;UAAE6F,QAAQ,CAACnF,MAAD,CAAR;;;;aAExB8F,EAAE,KAAKxG,UAAd,EAA0B;QACxBmG,EAAE,CAACL,IAAH,CAAQU,EAAR;;YACI/F,MAAM,CAACgG,IAAP,CAAY3G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAZ,CAAJ,EAA4C;UAC1CkC,EAAE,GAAG1G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAL;UACAA,WAAW;SAFb,MAGO;UACLkC,EAAE,GAAGxG,UAAL;;UAC2B;YAAE6F,QAAQ,CAACnF,MAAD,CAAR;;;;;UAG7ByF,EAAE,KAAKnG,UAAX,EAAuB;YACjBY,MAAM,CAAC6F,IAAP,CAAY3G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAZ,CAAJ,EAA4C;UAC1CkC,EAAE,GAAG1G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAL;UACAA,WAAW;SAFb,MAGO;UACLkC,EAAE,GAAGxG,UAAL;;UAC2B;YAAE6F,QAAQ,CAAChF,MAAD,CAAR;;;;YAE3B2F,EAAE,KAAKxG,UAAX,EAAuB;AACrBuE,AACA0B,UAAAA,EAAE,GAAGnF,MAAM,CAACqF,EAAD,CAAX;UACAH,EAAE,GAAGC,EAAL;SAHF,MAIO;UACL3B,WAAW,GAAG0B,EAAd;UACAA,EAAE,GAAGhG,UAAL;;OAdJ,MAgBO;QACLsE,WAAW,GAAG0B,EAAd;QACAA,EAAE,GAAGhG,UAAL;;KArCJ,MAuCO;MACLsE,WAAW,GAAG0B,EAAd;MACAA,EAAE,GAAGhG,UAAL;;;WAGKgG,EAAP;;;WAGOM,mBAAT,GAA+B;QACzBN,EAAJ,EAAQC,EAAR,EAAYE,EAAZ,EAAgBK,EAAhB,EAAoBG,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC;IAEAd,EAAE,GAAG1B,WAAL;IACA2B,EAAE,GAAGc,iBAAiB,EAAtB;;QACId,EAAE,KAAKjG,UAAX,EAAuB;MACrBmG,EAAE,GAAGE,UAAU,EAAf;;UACIF,EAAE,KAAKnG,UAAX,EAAuB;YACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;UACxCkC,EAAE,GAAGxF,MAAL;UACAsD,WAAW;SAFb,MAGO;UACLkC,EAAE,GAAGxG,UAAL;;UAC2B;YAAE6F,QAAQ,CAAC5E,MAAD,CAAR;;;;YAE3BuF,EAAE,KAAKxG,UAAX,EAAuB;UACrB2G,EAAE,GAAGN,UAAU,EAAf;;cACIM,EAAE,KAAK3G,UAAX,EAAuB;YACrB4G,EAAE,GAAGI,oBAAoB,EAAzB;;gBACIJ,EAAE,KAAK5G,UAAX,EAAuB;cACrB6G,EAAE,GAAGR,UAAU,EAAf;;kBACIQ,EAAE,KAAK7G,UAAX,EAAuB;oBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;kBACxCwC,EAAE,GAAG5F,OAAL;kBACAoD,WAAW;iBAFb,MAGO;kBACLwC,EAAE,GAAG9G,UAAL;;kBAC2B;oBAAE6F,QAAQ,CAAC1E,OAAD,CAAR;;;;oBAE3B2F,EAAE,KAAK9G,UAAX,EAAuB;AACrBuE,AACA0B,kBAAAA,EAAE,GAAG7E,OAAO,CAAC6E,EAAD,EAAKW,EAAL,CAAZ;kBACAZ,EAAE,GAAGC,EAAL;iBAHF,MAIO;kBACL3B,WAAW,GAAG0B,EAAd;kBACAA,EAAE,GAAGhG,UAAL;;eAdJ,MAgBO;gBACLsE,WAAW,GAAG0B,EAAd;gBACAA,EAAE,GAAGhG,UAAL;;aApBJ,MAsBO;cACLsE,WAAW,GAAG0B,EAAd;cACAA,EAAE,GAAGhG,UAAL;;WA1BJ,MA4BO;YACLsE,WAAW,GAAG0B,EAAd;YACAA,EAAE,GAAGhG,UAAL;;SAhCJ,MAkCO;UACLsE,WAAW,GAAG0B,EAAd;UACAA,EAAE,GAAGhG,UAAL;;OA5CJ,MA8CO;QACLsE,WAAW,GAAG0B,EAAd;QACAA,EAAE,GAAGhG,UAAL;;KAlDJ,MAoDO;MACLsE,WAAW,GAAG0B,EAAd;MACAA,EAAE,GAAGhG,UAAL;;;WAGKgG,EAAP;;;WAGOe,iBAAT,GAA6B;QACvBf,EAAJ,EAAQC,EAAR;IAEAD,EAAE,GAAG1B,WAAL;IACA2B,EAAE,GAAGgB,aAAa,EAAlB;;QACIhB,EAAE,KAAKjG,UAAX,EAAuB;AACrBuE,AACA0B,MAAAA,EAAE,GAAG3E,OAAO,CAAC2E,EAAD,CAAZ;;;IAEFD,EAAE,GAAGC,EAAL;WAEOD,EAAP;;;WAGOgB,oBAAT,GAAgC;QAC1BhB,EAAJ,EAAQC,EAAR,EAAYE,EAAZ,EAAgBK,EAAhB,EAAoBG,EAApB,EAAwBC,EAAxB;IAEAZ,EAAE,GAAG1B,WAAL;IACA2B,EAAE,GAAGiB,sBAAsB,EAA3B;;QACIjB,EAAE,KAAKjG,UAAX,EAAuB;MACrBmG,EAAE,GAAGE,UAAU,EAAf;;UACIF,EAAE,KAAKnG,UAAX,EAAuB;YACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,GAAtC,EAA2C;UACzCkC,EAAE,GAAGjF,OAAL;UACA+C,WAAW;SAFb,MAGO;UACLkC,EAAE,GAAGxG,UAAL;;UAC2B;YAAE6F,QAAQ,CAACrE,OAAD,CAAR;;;;YAE3BgF,EAAE,KAAKxG,UAAX,EAAuB;UACrB2G,EAAE,GAAGN,UAAU,EAAf;;cACIM,EAAE,KAAK3G,UAAX,EAAuB;YACrB4G,EAAE,GAAGI,oBAAoB,EAAzB;;gBACIJ,EAAE,KAAK5G,UAAX,EAAuB;AACrBuE,AACA0B,cAAAA,EAAE,GAAGxE,OAAO,CAACwE,EAAD,EAAKW,EAAL,CAAZ;cACAZ,EAAE,GAAGC,EAAL;aAHF,MAIO;cACL3B,WAAW,GAAG0B,EAAd;cACAA,EAAE,GAAGhG,UAAL;;WARJ,MAUO;YACLsE,WAAW,GAAG0B,EAAd;YACAA,EAAE,GAAGhG,UAAL;;SAdJ,MAgBO;UACLsE,WAAW,GAAG0B,EAAd;UACAA,EAAE,GAAGhG,UAAL;;OA1BJ,MA4BO;QACLsE,WAAW,GAAG0B,EAAd;QACAA,EAAE,GAAGhG,UAAL;;KAhCJ,MAkCO;MACLsE,WAAW,GAAG0B,EAAd;MACAA,EAAE,GAAGhG,UAAL;;;QAEEgG,EAAE,KAAKhG,UAAX,EAAuB;MACrBgG,EAAE,GAAGkB,sBAAsB,EAA3B;;;WAGKlB,EAAP;;;WAGOkB,sBAAT,GAAkC;QAC5BlB,EAAJ,EAAQC,EAAR,EAAYE,EAAZ,EAAgBK,EAAhB;IAEAR,EAAE,GAAG1B,WAAL;IACA2B,EAAE,GAAGkB,eAAe,EAApB;;QACIlB,EAAE,KAAKjG,UAAX,EAAuB;MACrBmG,EAAE,GAAGE,UAAU,EAAf;;UACIF,EAAE,KAAKnG,UAAX,EAAuB;QACrBwG,EAAE,GAAGU,sBAAsB,EAA3B;;YACIV,EAAE,KAAKxG,UAAX,EAAuB;AACrBuE,AACA0B,UAAAA,EAAE,GAAGvE,OAAO,CAACuE,EAAD,EAAKO,EAAL,CAAZ;UACAR,EAAE,GAAGC,EAAL;SAHF,MAIO;UACL3B,WAAW,GAAG0B,EAAd;UACAA,EAAE,GAAGhG,UAAL;;OARJ,MAUO;QACLsE,WAAW,GAAG0B,EAAd;QACAA,EAAE,GAAGhG,UAAL;;KAdJ,MAgBO;MACLsE,WAAW,GAAG0B,EAAd;MACAA,EAAE,GAAGhG,UAAL;;;QAEEgG,EAAE,KAAKhG,UAAX,EAAuB;MACrBgG,EAAE,GAAGmB,eAAe,EAApB;;;WAGKnB,EAAP;;;WAGOmB,eAAT,GAA2B;QACrBnB,EAAJ,EAAQC,EAAR,EAAYE,EAAZ,EAAgBK,EAAhB,EAAoBG,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B;IAEAb,EAAE,GAAG1B,WAAL;IACA2B,EAAE,GAAGgB,aAAa,EAAlB;;QACIhB,EAAE,KAAKjG,UAAX,EAAuB;UACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;QACxC6B,EAAE,GAAGxE,OAAL;QACA2C,WAAW;OAFb,MAGO;QACL6B,EAAE,GAAGnG,UAAL;;QAC2B;UAAE6F,QAAQ,CAACjE,OAAD,CAAR;;;;UAE3BuE,EAAE,KAAKnG,UAAX,EAAuB;QACrBwG,EAAE,GAAGW,eAAe,EAApB;;YACIX,EAAE,KAAKxG,UAAX,EAAuB;AACrBuE,AACA0B,UAAAA,EAAE,GAAGpE,OAAO,CAACoE,EAAD,EAAKO,EAAL,CAAZ;UACAR,EAAE,GAAGC,EAAL;SAHF,MAIO;UACL3B,WAAW,GAAG0B,EAAd;UACAA,EAAE,GAAGhG,UAAL;;OARJ,MAUO;QACLsE,WAAW,GAAG0B,EAAd;QACAA,EAAE,GAAGhG,UAAL;;KApBJ,MAsBO;MACLsE,WAAW,GAAG0B,EAAd;MACAA,EAAE,GAAGhG,UAAL;;;QAEEgG,EAAE,KAAKhG,UAAX,EAAuB;MACrBgG,EAAE,GAAG1B,WAAL;MACA2B,EAAE,GAAGmB,aAAa,EAAlB;;UACInB,EAAE,KAAKjG,UAAX,EAAuB;YACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;UACxC6B,EAAE,GAAGpE,OAAL;UACAuC,WAAW;SAFb,MAGO;UACL6B,EAAE,GAAGnG,UAAL;;UAC2B;YAAE6F,QAAQ,CAAC7D,OAAD,CAAR;;;;YAE3BmE,EAAE,KAAKnG,UAAX,EAAuB;AACrBuE,AACA0B,UAAAA,EAAE,GAAGhE,OAAO,CAACgE,EAAD,CAAZ;UACAD,EAAE,GAAGC,EAAL;SAHF,MAIO;UACL3B,WAAW,GAAG0B,EAAd;UACAA,EAAE,GAAGhG,UAAL;;OAdJ,MAgBO;QACLsE,WAAW,GAAG0B,EAAd;QACAA,EAAE,GAAGhG,UAAL;;;UAEEgG,EAAE,KAAKhG,UAAX,EAAuB;QACrBgG,EAAE,GAAG1B,WAAL;QACA2B,EAAE,GAAGmB,aAAa,EAAlB;;YACInB,EAAE,KAAKjG,UAAX,EAAuB;cACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;YACxC6B,EAAE,GAAGjE,OAAL;YACAoC,WAAW;WAFb,MAGO;YACL6B,EAAE,GAAGnG,UAAL;;YAC2B;cAAE6F,QAAQ,CAAC1D,OAAD,CAAR;;;;cAE3BgE,EAAE,KAAKnG,UAAX,EAAuB;AACrBuE,AACA0B,YAAAA,EAAE,GAAG7D,OAAO,CAAC6D,EAAD,CAAZ;YACAD,EAAE,GAAGC,EAAL;WAHF,MAIO;YACL3B,WAAW,GAAG0B,EAAd;YACAA,EAAE,GAAGhG,UAAL;;SAdJ,MAgBO;UACLsE,WAAW,GAAG0B,EAAd;UACAA,EAAE,GAAGhG,UAAL;;;YAEEgG,EAAE,KAAKhG,UAAX,EAAuB;UACrBgG,EAAE,GAAG1B,WAAL;UACA2B,EAAE,GAAGmB,aAAa,EAAlB;;cACInB,EAAE,KAAKjG,UAAX,EAAuB;gBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;cACxC6B,EAAE,GAAG9D,OAAL;cACAiC,WAAW;aAFb,MAGO;cACL6B,EAAE,GAAGnG,UAAL;;cAC2B;gBAAE6F,QAAQ,CAACvD,OAAD,CAAR;;;;gBAE3B6D,EAAE,KAAKnG,UAAX,EAAuB;AACrBuE,AACA0B,cAAAA,EAAE,GAAG1D,OAAO,CAAC0D,EAAD,CAAZ;cACAD,EAAE,GAAGC,EAAL;aAHF,MAIO;cACL3B,WAAW,GAAG0B,EAAd;cACAA,EAAE,GAAGhG,UAAL;;WAdJ,MAgBO;YACLsE,WAAW,GAAG0B,EAAd;YACAA,EAAE,GAAGhG,UAAL;;;cAEEgG,EAAE,KAAKhG,UAAX,EAAuB;YACrBgG,EAAE,GAAG1B,WAAL;YACA2B,EAAE,GAAGmB,aAAa,EAAlB;;gBACInB,EAAE,KAAKjG,UAAX,EAAuB;kBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,GAAtC,EAA2C;gBACzC6B,EAAE,GAAG3D,OAAL;gBACA8B,WAAW;eAFb,MAGO;gBACL6B,EAAE,GAAGnG,UAAL;;gBAC2B;kBAAE6F,QAAQ,CAACpD,OAAD,CAAR;;;;kBAE3B0D,EAAE,KAAKnG,UAAX,EAAuB;gBACrBwG,EAAE,GAAGa,eAAe,EAApB;;oBACIb,EAAE,KAAKxG,UAAX,EAAuB;sBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,GAAtC,EAA2C;oBACzCqC,EAAE,GAAGjE,OAAL;oBACA4B,WAAW;mBAFb,MAGO;oBACLqC,EAAE,GAAG3G,UAAL;;oBAC2B;sBAAE6F,QAAQ,CAAClD,OAAD,CAAR;;;;sBAE3BgE,EAAE,KAAK3G,UAAX,EAAuB;AACrBuE,AACA0B,oBAAAA,EAAE,GAAGrD,OAAO,CAACqD,EAAD,EAAKO,EAAL,CAAZ;oBACAR,EAAE,GAAGC,EAAL;mBAHF,MAIO;oBACL3B,WAAW,GAAG0B,EAAd;oBACAA,EAAE,GAAGhG,UAAL;;iBAdJ,MAgBO;kBACLsE,WAAW,GAAG0B,EAAd;kBACAA,EAAE,GAAGhG,UAAL;;eApBJ,MAsBO;gBACLsE,WAAW,GAAG0B,EAAd;gBACAA,EAAE,GAAGhG,UAAL;;aAhCJ,MAkCO;cACLsE,WAAW,GAAG0B,EAAd;cACAA,EAAE,GAAGhG,UAAL;;;gBAEEgG,EAAE,KAAKhG,UAAX,EAAuB;cACrBgG,EAAE,GAAG1B,WAAL;cACA2B,EAAE,GAAGmB,aAAa,EAAlB;;kBACInB,EAAE,KAAKjG,UAAX,EAAuB;oBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,GAAtC,EAA2C;kBACzC6B,EAAE,GAAG3D,OAAL;kBACA8B,WAAW;iBAFb,MAGO;kBACL6B,EAAE,GAAGnG,UAAL;;kBAC2B;oBAAE6F,QAAQ,CAACpD,OAAD,CAAR;;;;oBAE3B0D,EAAE,KAAKnG,UAAX,EAAuB;kBACrBwG,EAAE,GAAGa,eAAe,EAApB;;sBACIb,EAAE,KAAKxG,UAAX,EAAuB;wBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;sBACxCqC,EAAE,GAAG7D,OAAL;sBACAwB,WAAW;qBAFb,MAGO;sBACLqC,EAAE,GAAG3G,UAAL;;sBAC2B;wBAAE6F,QAAQ,CAAC9C,OAAD,CAAR;;;;wBAE3B4D,EAAE,KAAK3G,UAAX,EAAuB;0BACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,GAAtC,EAA2C;wBACzCsC,EAAE,GAAGlE,OAAL;wBACA4B,WAAW;uBAFb,MAGO;wBACLsC,EAAE,GAAG5G,UAAL;;wBAC2B;0BAAE6F,QAAQ,CAAClD,OAAD,CAAR;;;;0BAE3BiE,EAAE,KAAK5G,UAAX,EAAuB;AACrBuE,AACA0B,wBAAAA,EAAE,GAAGjD,OAAO,CAACiD,EAAD,EAAKO,EAAL,CAAZ;wBACAR,EAAE,GAAGC,EAAL;uBAHF,MAIO;wBACL3B,WAAW,GAAG0B,EAAd;wBACAA,EAAE,GAAGhG,UAAL;;qBAdJ,MAgBO;sBACLsE,WAAW,GAAG0B,EAAd;sBACAA,EAAE,GAAGhG,UAAL;;mBA1BJ,MA4BO;oBACLsE,WAAW,GAAG0B,EAAd;oBACAA,EAAE,GAAGhG,UAAL;;iBAhCJ,MAkCO;kBACLsE,WAAW,GAAG0B,EAAd;kBACAA,EAAE,GAAGhG,UAAL;;eA5CJ,MA8CO;gBACLsE,WAAW,GAAG0B,EAAd;gBACAA,EAAE,GAAGhG,UAAL;;;kBAEEgG,EAAE,KAAKhG,UAAX,EAAuB;gBACrBgG,EAAE,GAAG1B,WAAL;gBACA2B,EAAE,GAAGmB,aAAa,EAAlB;;oBACInB,EAAE,KAAKjG,UAAX,EAAuB;sBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,GAAtC,EAA2C;oBACzC6B,EAAE,GAAG3D,OAAL;oBACA8B,WAAW;mBAFb,MAGO;oBACL6B,EAAE,GAAGnG,UAAL;;oBAC2B;sBAAE6F,QAAQ,CAACpD,OAAD,CAAR;;;;sBAE3B0D,EAAE,KAAKnG,UAAX,EAAuB;wBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;sBACxCkC,EAAE,GAAG1D,OAAL;sBACAwB,WAAW;qBAFb,MAGO;sBACLkC,EAAE,GAAGxG,UAAL;;sBAC2B;wBAAE6F,QAAQ,CAAC9C,OAAD,CAAR;;;;wBAE3ByD,EAAE,KAAKxG,UAAX,EAAuB;sBACrB2G,EAAE,GAAGU,eAAe,EAApB;;0BACIV,EAAE,KAAK3G,UAAX,EAAuB;4BACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,GAAtC,EAA2C;0BACzCsC,EAAE,GAAGlE,OAAL;0BACA4B,WAAW;yBAFb,MAGO;0BACLsC,EAAE,GAAG5G,UAAL;;0BAC2B;4BAAE6F,QAAQ,CAAClD,OAAD,CAAR;;;;4BAE3BiE,EAAE,KAAK5G,UAAX,EAAuB;AACrBuE,AACA0B,0BAAAA,EAAE,GAAGhD,OAAO,CAACgD,EAAD,EAAKU,EAAL,CAAZ;0BACAX,EAAE,GAAGC,EAAL;yBAHF,MAIO;0BACL3B,WAAW,GAAG0B,EAAd;0BACAA,EAAE,GAAGhG,UAAL;;uBAdJ,MAgBO;wBACLsE,WAAW,GAAG0B,EAAd;wBACAA,EAAE,GAAGhG,UAAL;;qBApBJ,MAsBO;sBACLsE,WAAW,GAAG0B,EAAd;sBACAA,EAAE,GAAGhG,UAAL;;mBAhCJ,MAkCO;oBACLsE,WAAW,GAAG0B,EAAd;oBACAA,EAAE,GAAGhG,UAAL;;iBA5CJ,MA8CO;kBACLsE,WAAW,GAAG0B,EAAd;kBACAA,EAAE,GAAGhG,UAAL;;;oBAEEgG,EAAE,KAAKhG,UAAX,EAAuB;kBACrBgG,EAAE,GAAG1B,WAAL;kBACA2B,EAAE,GAAGmB,aAAa,EAAlB;;sBACInB,EAAE,KAAKjG,UAAX,EAAuB;wBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,GAAtC,EAA2C;sBACzC6B,EAAE,GAAG3D,OAAL;sBACA8B,WAAW;qBAFb,MAGO;sBACL6B,EAAE,GAAGnG,UAAL;;sBAC2B;wBAAE6F,QAAQ,CAACpD,OAAD,CAAR;;;;wBAE3B0D,EAAE,KAAKnG,UAAX,EAAuB;sBACrBwG,EAAE,GAAGa,eAAe,EAApB;;0BACIb,EAAE,KAAKxG,UAAX,EAAuB;4BACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;0BACxCqC,EAAE,GAAG7D,OAAL;0BACAwB,WAAW;yBAFb,MAGO;0BACLqC,EAAE,GAAG3G,UAAL;;0BAC2B;4BAAE6F,QAAQ,CAAC9C,OAAD,CAAR;;;;4BAE3B4D,EAAE,KAAK3G,UAAX,EAAuB;0BACrB4G,EAAE,GAAGS,eAAe,EAApB;;8BACIT,EAAE,KAAK5G,UAAX,EAAuB;gCACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,GAAtC,EAA2C;8BACzCuC,EAAE,GAAGnE,OAAL;8BACA4B,WAAW;6BAFb,MAGO;8BACLuC,EAAE,GAAG7G,UAAL;;8BAC2B;gCAAE6F,QAAQ,CAAClD,OAAD,CAAR;;;;gCAE3BkE,EAAE,KAAK7G,UAAX,EAAuB;AACrBuE,AACA0B,8BAAAA,EAAE,GAAG/C,OAAO,CAAC+C,EAAD,EAAKO,EAAL,EAASI,EAAT,CAAZ;8BACAZ,EAAE,GAAGC,EAAL;6BAHF,MAIO;8BACL3B,WAAW,GAAG0B,EAAd;8BACAA,EAAE,GAAGhG,UAAL;;2BAdJ,MAgBO;4BACLsE,WAAW,GAAG0B,EAAd;4BACAA,EAAE,GAAGhG,UAAL;;yBApBJ,MAsBO;0BACLsE,WAAW,GAAG0B,EAAd;0BACAA,EAAE,GAAGhG,UAAL;;uBAhCJ,MAkCO;wBACLsE,WAAW,GAAG0B,EAAd;wBACAA,EAAE,GAAGhG,UAAL;;qBAtCJ,MAwCO;sBACLsE,WAAW,GAAG0B,EAAd;sBACAA,EAAE,GAAGhG,UAAL;;mBAlDJ,MAoDO;oBACLsE,WAAW,GAAG0B,EAAd;oBACAA,EAAE,GAAGhG,UAAL;;;sBAEEgG,EAAE,KAAKhG,UAAX,EAAuB;oBACrBgG,EAAE,GAAGoB,aAAa,EAAlB;;;;;;;;;;WAUTpB,EAAP;;;WAGOoB,aAAT,GAAyB;QACnBpB,EAAJ,EAAQC,EAAR,EAAYE,EAAZ,EAAgBK,EAAhB;IAEAR,EAAE,GAAGe,iBAAiB,EAAtB;;QACIf,EAAE,KAAKhG,UAAX,EAAuB;MACrBgG,EAAE,GAAG1B,WAAL;MACA2B,EAAE,GAAGoB,eAAe,EAApB;;UACIpB,EAAE,KAAKjG,UAAX,EAAuB;AACrBuE,AACA0B,QAAAA,EAAE,GAAG9C,OAAO,CAAC8C,EAAD,CAAZ;;;MAEFD,EAAE,GAAGC,EAAL;;UACID,EAAE,KAAKhG,UAAX,EAAuB;QACrBgG,EAAE,GAAG1B,WAAL;;YACIxE,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;UACxC2B,EAAE,GAAG7C,OAAL;UACAkB,WAAW;SAFb,MAGO;UACL2B,EAAE,GAAGjG,UAAL;;UAC2B;YAAE6F,QAAQ,CAACxC,OAAD,CAAR;;;;YAE3B4C,EAAE,KAAKjG,UAAX,EAAuB;UACrBmG,EAAE,GAAGa,oBAAoB,EAAzB;;cACIb,EAAE,KAAKnG,UAAX,EAAuB;gBACjBF,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;cACxCkC,EAAE,GAAGlD,OAAL;cACAgB,WAAW;aAFb,MAGO;cACLkC,EAAE,GAAGxG,UAAL;;cAC2B;gBAAE6F,QAAQ,CAACtC,OAAD,CAAR;;;;gBAE3BiD,EAAE,KAAKxG,UAAX,EAAuB;AACrBuE,AACA0B,cAAAA,EAAE,GAAGzC,OAAO,CAAC2C,EAAD,CAAZ;cACAH,EAAE,GAAGC,EAAL;aAHF,MAIO;cACL3B,WAAW,GAAG0B,EAAd;cACAA,EAAE,GAAGhG,UAAL;;WAdJ,MAgBO;YACLsE,WAAW,GAAG0B,EAAd;YACAA,EAAE,GAAGhG,UAAL;;SApBJ,MAsBO;UACLsE,WAAW,GAAG0B,EAAd;UACAA,EAAE,GAAGhG,UAAL;;;;;WAKCgG,EAAP;;;WA4COiB,aAAT,GAAyB;QACnBjB,EAAJ,EAAQC,EAAR,EAAYE,EAAZ,EAAgBK,EAAhB;IAEAR,EAAE,GAAG1B,WAAL;IACA2B,EAAE,GAAGqB,wBAAwB,EAA7B;;QACIrB,EAAE,KAAKjG,UAAX,EAAuB;MACrBmG,EAAE,GAAG,EAAL;MACAK,EAAE,GAAGe,kBAAkB,EAAvB;;aACOf,EAAE,KAAKxG,UAAd,EAA0B;QACxBmG,EAAE,CAACL,IAAH,CAAQU,EAAR;QACAA,EAAE,GAAGe,kBAAkB,EAAvB;;;UAEEpB,EAAE,KAAKnG,UAAX,EAAuB;AACrBuE,AACA0B,QAAAA,EAAE,GAAGvC,OAAO,CAACuC,EAAD,EAAKE,EAAL,CAAZ;QACAH,EAAE,GAAGC,EAAL;OAHF,MAIO;QACL3B,WAAW,GAAG0B,EAAd;QACAA,EAAE,GAAGhG,UAAL;;KAbJ,MAeO;MACLsE,WAAW,GAAG0B,EAAd;MACAA,EAAE,GAAGhG,UAAL;;;WAGKgG,EAAP;;;WAGOsB,wBAAT,GAAoC;QAC9BtB,EAAJ;;QAEIlG,KAAK,CAACd,UAAN,CAAiBsF,WAAjB,MAAkC,EAAtC,EAA0C;MACxC0B,EAAE,GAAGrC,OAAL;MACAW,WAAW;KAFb,MAGO;MACL0B,EAAE,GAAGhG,UAAL;;MAC2B;QAAE6F,QAAQ,CAACjC,OAAD,CAAR;;;;QAE3BoC,EAAE,KAAKhG,UAAX,EAAuB;UACjB6D,OAAO,CAAC4C,IAAR,CAAa3G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAb,CAAJ,EAA6C;QAC3C0B,EAAE,GAAGlG,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAL;QACAA,WAAW;OAFb,MAGO;QACL0B,EAAE,GAAGhG,UAAL;;QAC2B;UAAE6F,QAAQ,CAAC/B,OAAD,CAAR;;;;;WAI1BkC,EAAP;;;WAGOuB,kBAAT,GAA8B;QACxBvB,EAAJ;IAEAA,EAAE,GAAGsB,wBAAwB,EAA7B;;QACItB,EAAE,KAAKhG,UAAX,EAAuB;UACjB+D,OAAO,CAAC0C,IAAR,CAAa3G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAb,CAAJ,EAA6C;QAC3C0B,EAAE,GAAGlG,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAL;QACAA,WAAW;OAFb,MAGO;QACL0B,EAAE,GAAGhG,UAAL;;QAC2B;UAAE6F,QAAQ,CAAC7B,OAAD,CAAR;;;;;WAI1BgC,EAAP;;;WAGOqB,eAAT,GAA2B;QACrBrB,EAAJ,EAAQC,EAAR,EAAYE,EAAZ;IAEAH,EAAE,GAAG1B,WAAL;IACA2B,EAAE,GAAG,EAAL;;QACIlC,OAAO,CAAC0C,IAAR,CAAa3G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAb,CAAJ,EAA6C;MAC3C6B,EAAE,GAAGrG,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAL;MACAA,WAAW;KAFb,MAGO;MACL6B,EAAE,GAAGnG,UAAL;;MAC2B;QAAE6F,QAAQ,CAAC7B,OAAD,CAAR;;;;QAE3BmC,EAAE,KAAKnG,UAAX,EAAuB;aACdmG,EAAE,KAAKnG,UAAd,EAA0B;QACxBiG,EAAE,CAACH,IAAH,CAAQK,EAAR;;YACIpC,OAAO,CAAC0C,IAAR,CAAa3G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAb,CAAJ,EAA6C;UAC3C6B,EAAE,GAAGrG,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAL;UACAA,WAAW;SAFb,MAGO;UACL6B,EAAE,GAAGnG,UAAL;;UAC2B;YAAE6F,QAAQ,CAAC7B,OAAD,CAAR;;;;KARnC,MAWO;MACLiC,EAAE,GAAGjG,UAAL;;;QAEEiG,EAAE,KAAKjG,UAAX,EAAuB;AACrBuE,AACA0B,MAAAA,EAAE,GAAGhC,OAAO,CAACgC,EAAD,CAAZ;;;IAEFD,EAAE,GAAGC,EAAL;WAEOD,EAAP;;;WAGOK,UAAT,GAAsB;QAChBL,EAAJ,EAAQC,EAAR;IAEAD,EAAE,GAAG,EAAL;;QACI5B,OAAO,CAACqC,IAAR,CAAa3G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAb,CAAJ,EAA6C;MAC3C2B,EAAE,GAAGnG,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAL;MACAA,WAAW;KAFb,MAGO;MACL2B,EAAE,GAAGjG,UAAL;;MAC2B;QAAE6F,QAAQ,CAACxB,OAAD,CAAR;;;;WAExB4B,EAAE,KAAKjG,UAAd,EAA0B;MACxBgG,EAAE,CAACF,IAAH,CAAQG,EAAR;;UACI7B,OAAO,CAACqC,IAAR,CAAa3G,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAb,CAAJ,EAA6C;QAC3C2B,EAAE,GAAGnG,KAAK,CAAC4G,MAAN,CAAapC,WAAb,CAAL;QACAA,WAAW;OAFb,MAGO;QACL2B,EAAE,GAAGjG,UAAL;;QAC2B;UAAE6F,QAAQ,CAACxB,OAAD,CAAR;;;;;WAI1B2B,EAAP;;;MAII7J,CAAC,GAAGqL,KAAR;EAGF1C,UAAU,GAAG1E,qBAAqB,EAAlC;;MAEI0E,UAAU,KAAK9E,UAAf,IAA6BsE,WAAW,KAAKxE,KAAK,CAACxB,MAAvD,EAA+D;WACtDwG,UAAP;GADF,MAEO;QACDA,UAAU,KAAK9E,UAAf,IAA6BsE,WAAW,GAAGxE,KAAK,CAACxB,MAArD,EAA6D;MAC3DuH,QAAQ,CAACZ,kBAAkB,EAAnB,CAAR;;;UAGIc,wBAAwB,CAC5BnB,mBAD4B,EAE5BD,cAAc,GAAG7E,KAAK,CAACxB,MAAvB,GAAgCwB,KAAK,CAAC4G,MAAN,CAAa/B,cAAb,CAAhC,GAA+D,IAFnC,EAG5BA,cAAc,GAAG7E,KAAK,CAACxB,MAAvB,GACIgH,mBAAmB,CAACX,cAAD,EAAiBA,cAAc,GAAG,CAAlC,CADvB,GAEIW,mBAAmB,CAACX,cAAD,EAAiBA,cAAjB,CALK,CAA9B;;;;AAUJ,WAAc,GAAG;EACf8C,WAAW,EAAEjK,eADE;EAEfkK,KAAK,EAAQ7H;CAFf;;AClpCA;;;;AAGA,AAAe,MAAM8H,WAAN,CAAkB;EAC/B1M,WAAW,CAAC2M,UAAD,EAAaC,eAAe,GAAG,EAA/B,EAAmC;SACvCC,SAAL,GAAiB,EAAjB;SACKC,OAAL,GAAe,EAAf;SACKC,IAAL,GAAY,IAAZ;SACKlN,IAAL,GAAY,CAAZ;SAEKmN,kBAAL,CAAwBJ,eAAxB;SACKK,OAAL,CAAaN,UAAb;;;EAGFK,kBAAkB,CAACJ,eAAD,EAAkB;SAC7B,IAAIvM,GAAT,IAAgBuM,eAAhB,EAAiC;WAC1BC,SAAL,CAAexM,GAAf,IAAsB,IAAI0B,OAAJ,CAAY6K,eAAe,CAACvM,GAAD,CAA3B,CAAtB;WACKyM,OAAL,CAAazM,GAAb,IAAoBuM,eAAe,CAACvM,GAAD,CAAnC;WACKR,IAAL;;;;EAIJoN,OAAO,CAACN,UAAD,EAAa;SACb,IAAIO,SAAT,IAAsBP,UAAtB,EAAkC;UAC5BO,SAAS,YAAYxM,UAAzB,EAAqC;aAC9BmM,SAAL,CAAeK,SAAS,CAACvM,QAAV,CAAmBJ,IAAlC,IAA0C,KAAK4M,iBAAL,CAAuBD,SAAS,CAACtM,UAAjC,CAA1C;;YAEIsM,SAAS,CAACtM,UAAV,YAAgCmB,OAApC,EAA6C;eACtC+K,OAAL,CAAaI,SAAS,CAACvM,QAAV,CAAmBJ,IAAhC,IAAwC2M,SAAS,CAACtM,UAAV,CAAqBT,KAA7D;eACKN,IAAL;;;;;SAKDkN,IAAL,GAAY,KAAKF,SAAL,CAAeE,IAA3B;;QACI,CAAC,KAAKA,IAAV,EAAgB;YACR,IAAIrL,KAAJ,CAAU,oCAAV,CAAN;;;;EAIJyL,iBAAiB,CAACC,IAAD,EAAO;;SAEjB,IAAI/M,GAAT,IAAgB+M,IAAhB,EAAsB;UAChBA,IAAI,CAAC/M,GAAD,CAAJ,YAAqBN,IAAzB,EAA+B;QAC7BqN,IAAI,CAAC/M,GAAD,CAAJ,GAAY,KAAK8M,iBAAL,CAAuBC,IAAI,CAAC/M,GAAD,CAA3B,CAAZ;;KAJkB;;;QASlB+M,IAAI,YAAY9M,QAApB,EAA8B;UACxBH,KAAK,GAAG,KAAK0M,SAAL,CAAeO,IAAI,CAAC7M,IAApB,CAAZ;UACIJ,KAAK,IAAI,IAAb,EACE,MAAM,IAAIuB,KAAJ,kCAAoC0L,IAAI,CAAC7M,IAAzC,EAAN;MAEF6M,IAAI,GAAG,KAAKD,iBAAL,CAAuBhN,KAAK,CAACK,IAAN,EAAvB,CAAP;;;WAGK4M,IAAP;;;;;ACxDJ,IAAMC,UAAU,GAAG,IAAIrL,SAAJ,EAAnB;;;;;;;;;AASA,AAAe,SAASsL,QAAT,CAAkBC,IAAlB,EAAwBC,UAAxB,EAAoC;EACjDD,IAAI,GAAG,IAAItM,aAAJ,CAAkBsM,IAAlB,EAAwBF,UAAxB,CAAP;EACAE,IAAI,CAACnN,aAAL;MAEIqN,SAAS,GAAG,IAAIC,KAAJ,CAAU,IAAIlO,GAAJ,EAAV,EAAmBgO,UAAnB,CAAhB;MACIG,YAAY,GAAG,IAAID,KAAJ,CAAUH,IAAI,CAACxM,QAAf,EAAyByM,UAAzB,CAAnB;MACII,OAAO,GAAG,CAACH,SAAD,EAAYE,YAAZ,CAAd,CANiD;;SAS1C,CAAP,EAAU;QACJpO,CAAC,GAAG,IAAR;;SAEK,IAAIgF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqJ,OAAO,CAACvK,MAA5B,EAAoCkB,CAAC,EAArC,EAAyC;UACnC,CAACqJ,OAAO,CAACrJ,CAAD,CAAP,CAAWsJ,MAAhB,EAAwB;QACtBtO,CAAC,GAAGqO,OAAO,CAACrJ,CAAD,CAAX;;;;;QAKAhF,CAAC,IAAI,IAAT,EAAe;;KAVP;;;IAeRA,CAAC,CAACsO,MAAF,GAAW,IAAX,CAfQ;;SAkBH,IAAIxO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmO,UAApB,EAAgCnO,CAAC,EAAjC,EAAqC;;;UAG/ByO,CAAC,GAAG,IAAItO,GAAJ,EAAR;;WACK,IAAI4K,CAAT,IAAc7K,CAAC,CAACwO,SAAhB,EAA2B;YACrB3D,CAAC,YAAYrI,OAAb,IAAwBqI,CAAC,CAACjK,KAAF,KAAYd,CAAxC,EAA2C;UACzCI,MAAM,CAACqO,CAAD,EAAI1D,CAAC,CAACjJ,SAAN,CAAN;;;;UAIA2M,CAAC,CAACjO,IAAF,KAAW,CAAf,EAAkB;;OAViB;;;UAe/BmO,EAAE,GAAG,CAAC,CAAV;;WACK,IAAIpM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgM,OAAO,CAACvK,MAA5B,EAAoCzB,CAAC,EAArC,EAAyC;YACnChC,KAAK,CAACkO,CAAD,EAAIF,OAAO,CAAChM,CAAD,CAAP,CAAWmM,SAAf,CAAT,EAAoC;UAClCC,EAAE,GAAGpM,CAAL;;;;;UAKAoM,EAAE,KAAK,CAAC,CAAZ,EAAe;;QAEbJ,OAAO,CAAC/C,IAAR,CAAa,IAAI6C,KAAJ,CAAUI,CAAV,EAAaN,UAAb,CAAb;QACAQ,EAAE,GAAGJ,OAAO,CAACvK,MAAR,GAAiB,CAAtB;;;MAGF9D,CAAC,CAAC0O,WAAF,CAAc5O,CAAd,IAAmB2O,EAAnB;;;;SAIGJ,OAAP;;;AAGF,MAAMF,KAAN,CAAY;EACV1N,WAAW,CAAC+N,SAAD,EAAYG,GAAZ,EAAiB;SACrBH,SAAL,GAAiBA,SAAjB;SACKE,WAAL,GAAmB,IAAIE,WAAJ,CAAgBD,GAAhB,CAAnB;SACKE,SAAL,GAAiBL,SAAS,CAACjO,GAAV,CAAcuN,UAAd,CAAjB;SACKQ,MAAL,GAAc,KAAd;SACKQ,IAAL,GAAY,IAAI7O,GAAJ,EAAZ;;SAEK,IAAI0K,GAAT,IAAgB6D,SAAhB,EAA2B;UACrB7D,GAAG,YAAYjI,GAAnB,EAAwB;aACjBoM,IAAL,CAAU1O,GAAV,CAAcuK,GAAG,CAAC3J,IAAlB;;;;;;;ACrFR,IAAM+N,aAAa,GAAG,CAAtB;AACA,IAAMC,UAAU,GAAG,CAAnB;;;;;;AAMA,AAAe,MAAMC,YAAN,CAAmB;EAChCxO,WAAW,CAACyO,GAAD,EAAM;SACVC,UAAL,GAAkBD,GAAG,CAACC,UAAtB;SACKN,SAAL,GAAiBK,GAAG,CAACL,SAArB;SACKC,IAAL,GAAYI,GAAG,CAACJ,IAAhB;;;;;;;;EAOFM,KAAK,CAACC,GAAD,EAAM;QACLC,IAAI,GAAG,IAAX;WACO;QACHC,MAAM,CAACC,QAAT,IAAqB;YACfC,KAAK,GAAGV,aAAZ;YACIW,QAAQ,GAAG,IAAf;YACIC,aAAa,GAAG,IAApB;YACIC,SAAS,GAAG,IAAhB;;aAEK,IAAI/E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwE,GAAG,CAACvL,MAAxB,EAAgC+G,CAAC,EAAjC,EAAqC;cAC/BgF,CAAC,GAAGR,GAAG,CAACxE,CAAD,CAAX;UAEA+E,SAAS,GAAGH,KAAZ;UACAA,KAAK,GAAGH,IAAI,CAACH,UAAL,CAAgBM,KAAhB,EAAuBI,CAAvB,CAAR;;cAEIJ,KAAK,KAAKT,UAAd,EAA0B;;gBAEpBU,QAAQ,IAAI,IAAZ,IAAoBC,aAAa,IAAI,IAArC,IAA6CA,aAAa,IAAID,QAAlE,EAA4E;oBACpE,CAACA,QAAD,EAAWC,aAAX,EAA0BL,IAAI,CAACR,IAAL,CAAUc,SAAV,CAA1B,CAAN;aAHsB;;;YAOxBH,KAAK,GAAGH,IAAI,CAACH,UAAL,CAAgBJ,aAAhB,EAA+Bc,CAA/B,CAAR;YACAH,QAAQ,GAAG,IAAX;WAdiC;;;cAkB/BD,KAAK,KAAKT,UAAV,IAAwBU,QAAQ,IAAI,IAAxC,EAA8C;YAC5CA,QAAQ,GAAG7E,CAAX;WAnBiC;;;cAuB/ByE,IAAI,CAACT,SAAL,CAAeY,KAAf,CAAJ,EAA2B;YACzBE,aAAa,GAAG9E,CAAhB;WAxBiC;;;cA4B/B4E,KAAK,KAAKT,UAAd,EAA0B;YACxBS,KAAK,GAAGV,aAAR;;SAnCe;;;YAwCfW,QAAQ,IAAI,IAAZ,IAAoBC,aAAa,IAAI,IAArC,IAA6CA,aAAa,IAAID,QAAlE,EAA4E;gBACpE,CAACA,QAAD,EAAWC,aAAX,EAA0BL,IAAI,CAACR,IAAL,CAAUW,KAAV,CAA1B,CAAN;;;;KA1CN;;;;;;;;;EAqDFK,KAAK,CAACT,GAAD,EAAMU,OAAN,EAAe;SACb,IAAI,CAAC5E,KAAD,EAAQhH,GAAR,EAAa2K,IAAb,CAAT,IAA+B,KAAKM,KAAL,CAAWC,GAAX,CAA/B,EAAgD;WACzC,IAAIW,GAAT,IAAgBlB,IAAhB,EAAsB;YAChB,OAAOiB,OAAO,CAACC,GAAD,CAAd,KAAwB,UAA5B,EAAwC;UACtCD,OAAO,CAACC,GAAD,CAAP,CAAa7E,KAAb,EAAoBhH,GAApB,EAAyBkL,GAAG,CAACnK,KAAJ,CAAUiG,KAAV,EAAiBhH,GAAG,GAAG,CAAvB,CAAzB;;;;;;;;ACxEH,SAAS+I,KAAT,CAAe+C,MAAf,EAAuB5C,eAAvB,EAAwC;MACzC6C,GAAG,GAAGC,OAAO,CAACjD,KAAR,CAAc+C,MAAd,CAAV;SACO,IAAI9C,WAAJ,CAAgB+C,GAAhB,EAAqB7C,eAArB,CAAP;;AAGF,AAAO,SAAS+C,KAAT,CAAeC,WAAf,EAA4B;MAC7BC,MAAM,GAAGvC,QAAQ,CAACsC,WAAW,CAAC7C,IAAb,EAAmB6C,WAAW,CAAC/P,IAA/B,CAArB;SAEO,IAAI2O,YAAJ,CAAiB;IACtBE,UAAU,EAAEmB,MAAM,CAACC,GAAP,CAAWvQ,CAAC,IAAI+D,KAAK,CAACyM,IAAN,CAAWxQ,CAAC,CAAC0O,WAAb,CAAhB,CADU;IAEtBG,SAAS,EAAEyB,MAAM,CAACC,GAAP,CAAWvQ,CAAC,IAAIA,CAAC,CAAC6O,SAAlB,CAFW;IAGtBC,IAAI,EAAEwB,MAAM,CAACC,GAAP,CAAWvQ,CAAC,IAAI+D,KAAK,CAACyM,IAAN,CAAWxQ,CAAC,CAAC8O,IAAb,CAAhB;GAHD,CAAP;;AAOF,AAAe,SAAS2B,OAAT,CAAiBR,MAAjB,EAAyB5C,eAAzB,EAA0C;SAChD+C,KAAK,CAAClD,KAAK,CAAC+C,MAAD,EAAS5C,eAAT,CAAN,CAAZ;;;;;;;"}